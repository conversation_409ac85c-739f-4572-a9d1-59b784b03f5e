# 🚀 APK资源释放接口使用指南

## 概述

`ApkAssetsExtractor` 是专门为APK中assets目录文件释放而设计的高性能接口类，针对大规模文件处理（7000+文件，1GB+总大小）进行了深度优化。

## 🎯 核心功能

### 智能文件过滤
- **assets/* 文件** → 目标根目录 `/*`
- **assets/Video/* 文件** → 目标目录 `/Video/*`
- 自动跳过不符合条件的文件和子目录

### 性能优化特性
- ✅ **4MB超大缓冲区** - 减少系统调用次数
- ✅ **智能文件排序** - 大文件优先处理
- ✅ **预分配策略** - 减少磁盘碎片
- ✅ **批量处理** - 每20个文件让出一帧
- ✅ **路径优化** - 减少字符串分配
- ✅ **多重安全策略** - 处理损坏文件

## 🛠️ 主要接口

### 1. 从APK释放assets资源
```csharp
// 基本用法
yield return ApkAssetsExtractor.ExtractAssetsFromApk(
    targetDirectory, overwrite, callback);

// 完整示例
var callback = new ZipUtilsEx.ZipCallback
{
    OnProgressCallback = (progress) => Debug.Log($"进度: {progress:P2}"),
    OnPackFinishedCall = (success, message) => Debug.Log($"结果: {message}")
};

yield return ApkAssetsExtractor.ExtractAssetsFromApk(
    "/storage/emulated/0/MyApp/Assets", true, callback);
```

### 2. 从ZIP文件释放assets资源
```csharp
// 用于测试或非Android平台
yield return ApkAssetsExtractor.ExtractAssetsFromZip(
    zipFilePath, targetDirectory, overwrite, callback);
```

### 3. 获取统计信息
```csharp
// 预览assets文件信息
var statistics = ApkAssetsExtractor.GetAssetsStatistics(zipFilePath);
Debug.Log(statistics.ToString());
// 输出: Assets统计: 总文件 1247 个, 总大小 1638.45MB, 根目录 1200 个, Video目录 47 个, 大文件 15 个, 小文件 1232 个
```

## 📊 回调系统

### ZipCallback 接口
```csharp
public class ZipCallback
{
    // 进度回调 (0.0 - 1.0)
    public System.Action<float> OnProgressCallback;
    
    // 文件处理前回调，返回false跳过该文件
    public System.Func<ZipEntry, bool> OnPreUnzipCall;
    
    // 文件处理后回调
    public System.Action<ZipEntry> OnPostUnzipCall;
    
    // 完成回调 (成功状态, 消息)
    public System.Action<bool, string> OnPackFinishedCall;
}
```

### 详细回调示例
```csharp
var callback = new ZipUtilsEx.ZipCallback
{
    OnProgressCallback = (progress) => 
    {
        progressBar.value = progress;
        progressText.text = $"{progress:P1}";
    },
    
    OnPreUnzipCall = (entry) => 
    {
        // 可以在这里过滤特定文件
        if (entry.FileName.EndsWith(".tmp"))
            return false; // 跳过临时文件
        
        Debug.Log($"开始处理: {entry.FileName}");
        return true;
    },
    
    OnPostUnzipCall = (entry) => 
    {
        Debug.Log($"完成处理: {entry.FileName}");
        processedFiles++;
    },
    
    OnPackFinishedCall = (success, message) => 
    {
        if (success)
        {
            Debug.Log($"✅ 释放成功: {message}");
            ShowSuccessDialog(message);
        }
        else
        {
            Debug.LogError($"❌ 释放失败: {message}");
            ShowErrorDialog(message);
        }
    }
};
```

## 🔧 配置参数

### 性能调优常量
```csharp
private const int ULTRA_BUFFER_SIZE = 4 * 1024 * 1024; // 4MB缓冲区
private const int BATCH_YIELD_SIZE = 20; // 批处理大小
private const long LARGE_FILE_THRESHOLD = 10 * 1024 * 1024; // 10MB大文件阈值
```

### 支持的路径模式
```csharp
private static readonly string[] SUPPORTED_ASSET_PATTERNS = {
    "assets/", // 根目录文件
    "assets/Video/" // Video目录文件
};
```

## 📁 路径映射规则

| 原始路径 | 目标路径 | 说明 |
|---------|---------|------|
| `assets/config.json` | `/config.json` | 根目录文件直接映射 |
| `assets/data.bin` | `/data.bin` | 根目录文件直接映射 |
| `assets/Video/intro.mp4` | `/Video/intro.mp4` | Video目录文件映射 |
| `assets/Video/tutorial.mp4` | `/Video/tutorial.mp4` | Video目录文件映射 |
| `assets/Textures/bg.png` | ❌ 跳过 | 不支持的子目录 |
| `assets/Audio/music.ogg` | ❌ 跳过 | 不支持的子目录 |

## 🧪 测试方法

### 1. 基本功能测试
```csharp
var test = GetComponent<ZipUtilsTest>();
test.TestApkAssetsExtraction(); // 测试基本释放功能
```

### 2. 性能基准测试
```csharp
test.ApkAssetsPerformanceTest(); // 性能基准测试
```

### 3. 统计信息查看
```csharp
var statistics = ApkAssetsExtractor.GetAssetsStatistics(zipPath);
Debug.Log(statistics);
```

## 📈 性能指标

### 预期性能
- **吞吐量**: 50-150 MB/s (取决于设备性能)
- **文件处理速度**: 100-500 文件/秒
- **内存使用**: 稳定在4-8MB
- **UI响应性**: 每20个文件让出一帧

### 性能评级标准
- **优秀**: ≥100MB/s
- **良好**: ≥50MB/s  
- **一般**: ≥20MB/s
- **需要优化**: <20MB/s

## 🛡️ 错误处理

### 多重安全策略
1. **重试策略**: 最多重试3次
2. **小缓冲区策略**: 使用8KB缓冲区
3. **逐字节策略**: 最后手段，适用于小文件(<1MB)

### 常见错误处理
```csharp
// 平台检查
if (Application.platform != RuntimePlatform.Android)
{
    callback?.OnPackFinishedCall?.Invoke(false, "仅支持Android平台");
    return;
}

// 文件存在检查
if (!File.Exists(apkPath))
{
    callback?.OnPackFinishedCall?.Invoke(false, $"APK文件不存在: {apkPath}");
    return;
}

// 损坏文件处理
if (!extractSuccess)
{
    Debug.LogWarning("常规提取失败，尝试安全策略");
    extractSuccess = SafeExtractAssetFile(entry, targetPath, buffer, overwrite);
}
```

## 🚀 最佳实践

### 1. 使用建议
- **预检查**: 使用 `GetAssetsStatistics` 预览文件信息
- **进度显示**: 实现进度条提升用户体验
- **错误处理**: 妥善处理失败情况
- **资源管理**: 及时清理临时文件

### 2. 性能优化
- **存储选择**: 优先使用内部存储提升I/O性能
- **空间检查**: 确保有足够的磁盘空间
- **后台处理**: 避免在主线程执行
- **批量操作**: 利用批处理机制

### 3. 用户体验
- **进度反馈**: 实时显示处理进度
- **状态提示**: 清晰的成功/失败提示
- **可取消**: 提供取消操作选项
- **错误恢复**: 支持重试机制

## 📋 完整使用示例

```csharp
public class AssetsManager : MonoBehaviour
{
    [SerializeField] private Slider progressBar;
    [SerializeField] private Text statusText;
    
    public IEnumerator ExtractGameAssets()
    {
        statusText.text = "正在准备释放资源...";
        
        // 获取统计信息
        string apkPath = ZipUtilsEx.GetApkPath();
        var statistics = ApkAssetsExtractor.GetAssetsStatistics(apkPath);
        Debug.Log($"将要释放: {statistics}");
        
        // 设置回调
        var callback = new ZipUtilsEx.ZipCallback
        {
            OnProgressCallback = (progress) => 
            {
                progressBar.value = progress;
                statusText.text = $"正在释放资源... {progress:P1}";
            },
            
            OnPackFinishedCall = (success, message) => 
            {
                if (success)
                {
                    statusText.text = "资源释放完成！";
                    Debug.Log($"✅ {message}");
                }
                else
                {
                    statusText.text = "资源释放失败！";
                    Debug.LogError($"❌ {message}");
                }
            }
        };
        
        // 执行释放
        string targetDir = Path.Combine(Application.persistentDataPath, "GameAssets");
        yield return ApkAssetsExtractor.ExtractAssetsFromApk(targetDir, true, callback);
    }
}
```

这个APK资源释放接口提供了完整的、高性能的、易于使用的assets文件释放解决方案，特别适合需要从APK中提取大量资源文件的Unity应用。
