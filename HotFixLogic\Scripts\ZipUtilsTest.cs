using System.Collections;
using System.IO;
using UnityEngine;
using HotFix;

namespace HotFix
{
    /// <summary>
    /// ZIP工具类测试脚本
    /// 用于验证所有方法是否正常工作
    /// </summary>
    public class ZipUtilsTest : MonoBehaviour
    {
        [Header("测试配置")]
        public string testZipPath = "";
        public string testOutputPath = "";
        public bool runTestOnStart = false;

        private void Start()
        {
            if (runTestOnStart && !string.IsNullOrEmpty(testZipPath))
            {
                StartCoroutine(RunAllTests());
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        private IEnumerator RunAllTests()
        {
            Debug.Log("=== ZIP工具类测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"测试进度: {progress:P2}"),
                OnPackFinishedCall = (success, message) => Debug.Log($"测试结果: {success}, {message}")
            };

            // 测试1: 基础解压功能
            Debug.Log("测试1: 基础解压功能");
            yield return TestBasicDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试2: 优化解压功能
            Debug.Log("测试2: 优化解压功能");
            yield return TestOptimizedDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试3: 高性能解压功能
            Debug.Log("测试3: 高性能解压功能");
            yield return TestHighPerformanceDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试4: 可靠解压功能
            Debug.Log("测试4: 可靠解压功能");
            yield return TestReliableDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试5: 超高性能单线程解压功能
            Debug.Log("测试5: 超高性能单线程解压功能");
            yield return TestUltraFastDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试6: Android APK解压功能（仅Android平台）
            if (Application.platform == RuntimePlatform.Android)
            {
                Debug.Log("测试6: Android APK解压功能");
                yield return TestAndroidApkDecompression(callback);
            }

            Debug.Log("=== 所有测试完成 ===");
        }

        /// <summary>
        /// 测试基础解压功能
        /// </summary>
        private IEnumerator TestBasicDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_basic";
            yield return ZipUtilsEx.DecompressionCoroutine(testZipPath, outputPath, true, callback, 4);
        }

        /// <summary>
        /// 测试优化解压功能
        /// </summary>
        private IEnumerator TestOptimizedDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_optimized";
            yield return ZipUtilsEx.DecompressionCoroutineOptimized(testZipPath, outputPath, true, callback, 8);
        }

        /// <summary>
        /// 测试高性能解压功能
        /// </summary>
        private IEnumerator TestHighPerformanceDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_highperf";
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                testZipPath, outputPath, true, callback, 8);
        }

        /// <summary>
        /// 测试可靠解压功能
        /// </summary>
        private IEnumerator TestReliableDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_reliable";
            yield return HighPerformanceZipExtractor.DecompressionCoroutineReliable(
                testZipPath, outputPath, true, callback, 4);
        }

        /// <summary>
        /// 测试超高性能单线程解压功能
        /// </summary>
        private IEnumerator TestUltraFastDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_ultrafast";
            yield return HighPerformanceZipExtractor.DecompressionCoroutineUltraFast(
                testZipPath, outputPath, true, callback);
        }

        /// <summary>
        /// 快速测试文件复制修复
        /// </summary>
        [ContextMenu("快速测试文件复制修复")]
        public void QuickTestFileCopyFix()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(QuickTestCoroutine());
        }

        private IEnumerator QuickTestCoroutine()
        {
            Debug.Log("=== 快速测试文件复制修复 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"进度: {progress:P2}"),
                OnPackFinishedCall = (success, message) =>
                {
                    //Debug.Log($"结果: {success ? "成功" : "失败"}");
                    if (success)
                    {
                        Debug.Log($"结果:  成功");
                    }
                    else
                    {
                        Debug.Log($"结果:  失败");
                    }
                    Debug.Log($"消息: {message}");
                }
            };

            string outputPath = testOutputPath + "_quick_test";

            // 先测试可靠版本
            Debug.Log("测试可靠版本...");
            yield return HighPerformanceZipExtractor.DecompressionCoroutineReliable(
                testZipPath, outputPath + "_reliable", true, callback, 4);

            yield return new WaitForSeconds(1f);

            // 再测试修复后的内存映射版本
            Debug.Log("测试修复后的内存映射版本...");
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                testZipPath, outputPath + "_memory_mapped", true, callback, 8);

            Debug.Log("=== 快速测试完成 ===");
        }

        /// <summary>
        /// 诊断异常问题
        /// </summary>
        [ContextMenu("诊断异常问题")]
        public void DiagnoseExceptionIssues()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(DiagnoseExceptionCoroutine());
        }

        private IEnumerator DiagnoseExceptionCoroutine()
        {
            Debug.Log("=== 开始诊断异常问题 ===");

            // 检查文件是否存在
            if (!System.IO.File.Exists(testZipPath))
            {
                Debug.LogError($"ZIP文件不存在: {testZipPath}");
                yield break;
            }

            Debug.Log($"ZIP文件存在: {testZipPath}");
            var fileInfo = new System.IO.FileInfo(testZipPath);
            Debug.Log($"文件大小: {fileInfo.Length} bytes");

            // 检查输出目录
            string outputPath = testOutputPath + "_diagnosis";
            if (System.IO.Directory.Exists(outputPath))
            {
                Debug.Log($"清理旧的输出目录: {outputPath}");
                System.IO.Directory.Delete(outputPath, true);
            }

            Debug.Log($"创建输出目录: {outputPath}");
            System.IO.Directory.CreateDirectory(outputPath);

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"诊断进度: {progress:P2}"),
                OnPreUnzipCall = (entry) =>
                {
                    Debug.Log($"准备解压: {entry.FileName} ({entry.UncompressedSize} bytes)");
                    return true;
                },
                OnPostUnzipCall = (entry) =>
                {
                    Debug.Log($"完成解压: {entry.FileName}");
                },
                OnPackFinishedCall = (success, message) =>
                {
                    Debug.Log($"诊断结果: {(success ? "成功" : "失败")}");
                    Debug.Log($"诊断消息: {message}");
                }
            };

            // 使用较少的线程数进行测试
            Debug.Log("开始诊断测试，使用2个线程...");
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                testZipPath, outputPath, true, callback, 2);

            // 检查结果
            if (System.IO.Directory.Exists(outputPath))
            {
                var files = System.IO.Directory.GetFiles(outputPath, "*", System.IO.SearchOption.AllDirectories);
                Debug.Log($"诊断完成，输出目录包含 {files.Length} 个文件");

                /*foreach (var file in files.Take(5)) // 只显示前5个文件
                {
                    var info = new System.IO.FileInfo(file);
                    Debug.Log($"  文件: {file}, 大小: {info.Length} bytes");
                }*/

                if (files.Length > 5)
                {
                    Debug.Log($"  ... 还有 {files.Length - 5} 个文件");
                }
            }

            Debug.Log("=== 诊断完成 ===");
        }

        /// <summary>
        /// 性能对比测试 - 单线程 vs 多线程
        /// </summary>
        [ContextMenu("性能对比测试")]
        public void PerformanceComparisonTest()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(PerformanceComparisonCoroutine());
        }

        private IEnumerator PerformanceComparisonCoroutine()
        {
            Debug.Log("=== 性能对比测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => { /* 静默处理 */ },
                OnPackFinishedCall = (success, message) =>
                {
                    Debug.Log($"测试结果: {(success ? "成功" : "失败")} - {message}");
                }
            };

            // 测试1: 超高性能单线程方案
            Debug.Log("测试1: 超高性能单线程方案");
            string outputPath1 = testOutputPath + "_perf_ultrafast";
            var stopwatch1 = System.Diagnostics.Stopwatch.StartNew();
            yield return HighPerformanceZipExtractor.DecompressionCoroutineUltraFast(
                testZipPath, outputPath1, true, callback);
            stopwatch1.Stop();
            Debug.Log($"超高性能单线程耗时: {stopwatch1.Elapsed.TotalSeconds:F2}秒");

            yield return new WaitForSeconds(2f);

            // 测试2: 可靠单线程方案
            Debug.Log("测试2: 可靠单线程方案");
            string outputPath2 = testOutputPath + "_perf_reliable";
            var stopwatch2 = System.Diagnostics.Stopwatch.StartNew();
            yield return HighPerformanceZipExtractor.DecompressionCoroutineReliable(
                testZipPath, outputPath2, true, callback, 1);
            stopwatch2.Stop();
            Debug.Log($"可靠单线程耗时: {stopwatch2.Elapsed.TotalSeconds:F2}秒");

            yield return new WaitForSeconds(2f);

            // 测试3: 原始优化方案
            Debug.Log("测试3: 原始优化方案");
            string outputPath3 = testOutputPath + "_perf_optimized";
            var stopwatch3 = System.Diagnostics.Stopwatch.StartNew();
            yield return ZipUtilsEx.DecompressionCoroutineOptimized(
                testZipPath, outputPath3, true, callback, 1);
            stopwatch3.Stop();
            Debug.Log($"原始优化方案耗时: {stopwatch3.Elapsed.TotalSeconds:F2}秒");

            // 性能对比结果
            Debug.Log("=== 性能对比结果 ===");
            Debug.Log($"超高性能单线程: {stopwatch1.Elapsed.TotalSeconds:F2}秒");
            Debug.Log($"可靠单线程: {stopwatch2.Elapsed.TotalSeconds:F2}秒");
            Debug.Log($"原始优化方案: {stopwatch3.Elapsed.TotalSeconds:F2}秒");

            double improvement1 = stopwatch3.Elapsed.TotalSeconds / stopwatch1.Elapsed.TotalSeconds;
            double improvement2 = stopwatch2.Elapsed.TotalSeconds / stopwatch1.Elapsed.TotalSeconds;

            Debug.Log($"超高性能 vs 原始方案提升: {improvement1:F2}倍");
            Debug.Log($"超高性能 vs 可靠方案提升: {improvement2:F2}倍");

            Debug.Log("=== 性能对比测试完成 ===");
        }

        /// <summary>
        /// 测试损坏ZIP文件处理
        /// </summary>
        [ContextMenu("测试损坏ZIP文件处理")]
        public void TestCorruptedZipHandling()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(TestCorruptedZipCoroutine());
        }

        private IEnumerator TestCorruptedZipCoroutine()
        {
            Debug.Log("=== 损坏ZIP文件处理测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"处理进度: {progress:P2}"),
                OnPreUnzipCall = (entry) =>
                {
                    Debug.Log($"准备处理: {entry.FileName} (压缩: {entry.CompressedSize}, 未压缩: {entry.UncompressedSize})");
                    return true;
                },
                OnPostUnzipCall = (entry) =>
                {
                    Debug.Log($"成功处理: {entry.FileName}");
                },
                OnPackFinishedCall = (success, message) =>
                {
                    Debug.Log($"处理结果: {(success ? "成功" : "失败")} - {message}");
                }
            };

            string outputPath = testOutputPath + "_corrupted_test";

            // 清理旧的输出目录
            if (System.IO.Directory.Exists(outputPath))
            {
                System.IO.Directory.Delete(outputPath, true);
            }
            System.IO.Directory.CreateDirectory(outputPath);

            Debug.Log("使用增强错误处理的超高性能方法...");
            yield return HighPerformanceZipExtractor.DecompressionCoroutineUltraFast(
                testZipPath, outputPath, true, callback);

            // 检查结果
            if (System.IO.Directory.Exists(outputPath))
            {
                var files = System.IO.Directory.GetFiles(outputPath, "*", System.IO.SearchOption.AllDirectories);
                Debug.Log($"处理完成，输出目录包含 {files.Length} 个文件");

                // 检查是否有损坏文件的日志
                Debug.Log("请查看上方日志，确认损坏文件是否被正确处理");
            }

            Debug.Log("=== 损坏ZIP文件处理测试完成 ===");
        }

        /// <summary>
        /// 测试单个文件的安全提取
        /// </summary>
        [ContextMenu("测试安全提取策略")]
        public void TestSafeExtractionStrategy()
        {
            Debug.Log("=== 安全提取策略测试 ===");

            // 这里可以添加对特定损坏文件的测试
            // 由于需要实际的损坏ZIP文件，这里主要是提供测试框架

            Debug.Log("安全提取策略包括:");
            Debug.Log("1. 重试策略 - 多次尝试提取");
            Debug.Log("2. 小缓冲区策略 - 使用8KB缓冲区");
            Debug.Log("3. 逐字节策略 - 最后手段，适用于小文件");
            Debug.Log("4. ZIP条目验证 - 检查压缩比、文件名等");

            Debug.Log("=== 安全提取策略测试完成 ===");
        }

        /// <summary>
        /// 验证DecompressionCoroutineUltraFast函数可用性
        /// </summary>
        [ContextMenu("验证UltraFast函数")]
        public void VerifyUltraFastFunction()
        {
            Debug.Log("=== 验证DecompressionCoroutineUltraFast函数 ===");

            // 检查函数是否存在
            var method = typeof(HighPerformanceZipExtractor).GetMethod("DecompressionCoroutineUltraFast");
            if (method != null)
            {
                Debug.Log("✅ DecompressionCoroutineUltraFast函数存在");
                Debug.Log($"   返回类型: {method.ReturnType.Name}");
                Debug.Log($"   参数数量: {method.GetParameters().Length}");

                var parameters = method.GetParameters();
                for (int i = 0; i < parameters.Length; i++)
                {
                    var param = parameters[i];
                    Debug.Log($"   参数{i + 1}: {param.ParameterType.Name} {param.Name}");
                }
            }
            else
            {
                Debug.LogError("❌ DecompressionCoroutineUltraFast函数不存在");
            }

            // 检查类是否存在
            var type = typeof(HighPerformanceZipExtractor);
            Debug.Log($"✅ HighPerformanceZipExtractor类存在: {type.FullName}");

            Debug.Log("=== 验证完成 ===");
        }

        /// <summary>
        /// 快速功能测试
        /// </summary>
        [ContextMenu("快速功能测试")]
        public void QuickFunctionTest()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(QuickFunctionTestCoroutine());
        }

        private IEnumerator QuickFunctionTestCoroutine()
        {
            Debug.Log("=== 快速功能测试 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"进度: {progress:P2}"),
                OnPackFinishedCall = (success, message) =>
                {
                    Debug.Log($"结果: {(success ? "成功" : "失败")} - {message}");
                }
            };

            string outputPath = testOutputPath + "_quick_function_test";

            //try
            //{
                Debug.Log("调用DecompressionCoroutineUltraFast...");
                yield return HighPerformanceZipExtractor.DecompressionCoroutineUltraFast(
                    testZipPath, outputPath, true, callback);
                Debug.Log("✅ 函数调用成功");
            //}
            //catch (System.Exception ex)
            //{
            //    Debug.LogError($"❌ 函数调用失败: {ex.Message}");
            //}

            Debug.Log("=== 快速功能测试完成 ===");
        }

        /// <summary>
        /// 测试Android APK解压功能
        /// </summary>
        private IEnumerator TestAndroidApkDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_apk";
            yield return HighPerformanceZipExtractor.DecompressApkAssetsUltraFast(outputPath, true, callback);
        }

        /// <summary>
        /// 测试文件系统优化功能
        /// </summary>
        [ContextMenu("测试文件系统优化")]
        public void TestFileSystemOptimization()
        {
            Debug.Log("测试文件系统优化功能");
            
            try
            {
                // 测试预分配空间
                string testFile = Application.temporaryCachePath + "/test_prealloc.tmp";
                FileSystemOptimizer.PreallocateSpace(testFile, 1024 * 1024); // 1MB
                Debug.Log("预分配空间测试成功");

                // 测试文件系统缓存优化
                FileSystemOptimizer.OptimizeFileSystemCache();
                Debug.Log("文件系统缓存优化测试成功");

                // 清理测试文件
                if (System.IO.File.Exists(testFile))
                {
                    System.IO.File.Delete(testFile);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"文件系统优化测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试对象池功能
        /// </summary>
        [ContextMenu("测试对象池")]
        public void TestObjectPool()
        {
            Debug.Log("测试对象池功能");

            try
            {
                // 测试缓冲区池
                var buffer1 = ZipUtilsEx.ZipUtilsObjectPool.GetBuffer();
                var buffer2 = ZipUtilsEx.ZipUtilsObjectPool.GetBuffer();
                Debug.Log($"获取缓冲区: {buffer1.Length}, {buffer2.Length}");

                ZipUtilsEx.ZipUtilsObjectPool.ReturnBuffer(buffer1);
                ZipUtilsEx.ZipUtilsObjectPool.ReturnBuffer(buffer2);
                Debug.Log("缓冲区池测试成功");

                // 测试StringBuilder池
                var sb1 = ZipUtilsEx.ZipUtilsObjectPool.GetStringBuilder();
                var sb2 = ZipUtilsEx.ZipUtilsObjectPool.GetStringBuilder();
                sb1.Append("Test1");
                sb2.Append("Test2");
                Debug.Log($"StringBuilder测试: {sb1}, {sb2}");

                ZipUtilsEx.ZipUtilsObjectPool.ReturnStringBuilder(sb1);
                ZipUtilsEx.ZipUtilsObjectPool.ReturnStringBuilder(sb2);
                Debug.Log("StringBuilder池测试成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"对象池测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取APK路径测试
        /// </summary>
        [ContextMenu("测试APK路径获取")]
        public void TestGetApkPath()
        {
            string apkPath = ZipUtilsEx.GetApkPath();
            Debug.Log($"APK路径: {apkPath}");
            
            if (!string.IsNullOrEmpty(apkPath))
            {
                Debug.Log($"APK文件存在: {System.IO.File.Exists(apkPath)}");
            }
        }

        /// <summary>
        /// 测试文件复制成功性
        /// </summary>
        [ContextMenu("测试文件复制成功性")]
        public void TestFileCopySuccess()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(FileCopyTestCoroutine());
        }

        private IEnumerator FileCopyTestCoroutine()
        {
            Debug.Log("=== 文件复制成功性测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"解压进度: {progress:P2}"),
                OnPackFinishedCall = (success, message) =>
                {
                    Debug.Log($"解压结果: {success}");
                    Debug.Log($"消息: {message}");

                    if (success)
                    {
                        // 验证文件是否真的被复制了
                        StartCoroutine(VerifyExtractedFiles());
                    }
                }
            };

            string outputPath = testOutputPath + "_copy_test";

            // 使用可靠方法解压
            yield return HighPerformanceZipExtractor.DecompressionCoroutineReliable(
                testZipPath, outputPath, true, callback, 4);

            Debug.Log("=== 文件复制成功性测试完成 ===");
        }

        private IEnumerator VerifyExtractedFiles()
        {
            yield return new WaitForSeconds(1f);

            string outputPath = testOutputPath + "_copy_test";

            if (Directory.Exists(outputPath))
            {
                var files = Directory.GetFiles(outputPath, "*", SearchOption.AllDirectories);
                Debug.Log($"找到 {files.Length} 个解压文件:");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    Debug.Log($"文件: {file}, 大小: {fileInfo.Length} bytes");
                }
            }
            else
            {
                Debug.LogError($"输出目录不存在: {outputPath}");
            }
        }

        /// <summary>
        /// 测试文件完整性验证
        /// </summary>
        [ContextMenu("测试文件完整性验证")]
        public void TestFileIntegrityValidation()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(FileIntegrityTestCoroutine());
        }

        private IEnumerator FileIntegrityTestCoroutine()
        {
            Debug.Log("=== 文件完整性验证测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"解压进度: {progress:P2}"),
                OnPackFinishedCall = (success, message) =>
                {
                    Debug.Log($"解压结果: {success}");
                    Debug.Log($"消息: {message}");
                }
            };

            string outputPath = testOutputPath + "_integrity_test";

            // 使用高性能方法解压并验证完整性
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                testZipPath, outputPath, true, callback, 8);

            Debug.Log("=== 文件完整性验证测试完成 ===");
        }

        /// <summary>
        /// 性能基准测试
        /// </summary>
        [ContextMenu("运行性能基准测试")]
        public void RunPerformanceBenchmark()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(PerformanceBenchmarkCoroutine());
        }

        private IEnumerator PerformanceBenchmarkCoroutine()
        {
            Debug.Log("=== 性能基准测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => { /* 静默处理 */ },
                OnPackFinishedCall = (success, message) => Debug.Log($"解压完成: {success}")
            };

            // 基准测试：原始方法
            var stopwatch1 = System.Diagnostics.Stopwatch.StartNew();
            yield return ZipUtilsEx.DecompressionCoroutine(testZipPath, testOutputPath + "_benchmark1", true, callback, 1);
            stopwatch1.Stop();
            Debug.Log($"原始方法耗时: {stopwatch1.Elapsed.TotalSeconds:F2}秒");

            yield return new WaitForSeconds(2f);

            // 基准测试：高性能方法
            var stopwatch2 = System.Diagnostics.Stopwatch.StartNew();
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                testZipPath, testOutputPath + "_benchmark2", true, callback, 8);
            stopwatch2.Stop();
            Debug.Log($"高性能方法耗时: {stopwatch2.Elapsed.TotalSeconds:F2}秒");

            // 计算性能提升
            double improvement = stopwatch1.Elapsed.TotalSeconds / stopwatch2.Elapsed.TotalSeconds;
            Debug.Log($"=== 性能提升: {improvement:F2}倍 ===");
        }
    }
}
