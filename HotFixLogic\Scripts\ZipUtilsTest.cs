using System.Collections;
using UnityEngine;
using HotFix;

namespace HotFix
{
    /// <summary>
    /// ZIP工具类测试脚本
    /// 用于验证所有方法是否正常工作
    /// </summary>
    public class ZipUtilsTest : MonoBehaviour
    {
        [Header("测试配置")]
        public string testZipPath = "";
        public string testOutputPath = "";
        public bool runTestOnStart = false;

        private void Start()
        {
            if (runTestOnStart && !string.IsNullOrEmpty(testZipPath))
            {
                StartCoroutine(RunAllTests());
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        private IEnumerator RunAllTests()
        {
            Debug.Log("=== ZIP工具类测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"测试进度: {progress:P2}"),
                OnPackFinishedCall = (success, message) => Debug.Log($"测试结果: {success}, {message}")
            };

            // 测试1: 基础解压功能
            Debug.Log("测试1: 基础解压功能");
            yield return TestBasicDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试2: 优化解压功能
            Debug.Log("测试2: 优化解压功能");
            yield return TestOptimizedDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试3: 高性能解压功能
            Debug.Log("测试3: 高性能解压功能");
            yield return TestHighPerformanceDecompression(callback);

            yield return new WaitForSeconds(1f);

            // 测试4: Android APK解压功能（仅Android平台）
            if (Application.platform == RuntimePlatform.Android)
            {
                Debug.Log("测试4: Android APK解压功能");
                yield return TestAndroidApkDecompression(callback);
            }

            Debug.Log("=== 所有测试完成 ===");
        }

        /// <summary>
        /// 测试基础解压功能
        /// </summary>
        private IEnumerator TestBasicDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_basic";
            yield return ZipUtilsEx.DecompressionCoroutine(testZipPath, outputPath, true, callback, 4);
        }

        /// <summary>
        /// 测试优化解压功能
        /// </summary>
        private IEnumerator TestOptimizedDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_optimized";
            yield return ZipUtilsEx.DecompressionCoroutineOptimized(testZipPath, outputPath, true, callback, 8);
        }

        /// <summary>
        /// 测试高性能解压功能
        /// </summary>
        private IEnumerator TestHighPerformanceDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_highperf";
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                testZipPath, outputPath, true, callback, 8);
        }

        /// <summary>
        /// 测试Android APK解压功能
        /// </summary>
        private IEnumerator TestAndroidApkDecompression(ZipUtilsEx.ZipCallback callback)
        {
            string outputPath = testOutputPath + "_apk";
            yield return HighPerformanceZipExtractor.DecompressApkAssetsUltraFast(outputPath, true, callback);
        }

        /// <summary>
        /// 测试文件系统优化功能
        /// </summary>
        [ContextMenu("测试文件系统优化")]
        public void TestFileSystemOptimization()
        {
            Debug.Log("测试文件系统优化功能");
            
            try
            {
                // 测试预分配空间
                string testFile = Application.temporaryCachePath + "/test_prealloc.tmp";
                FileSystemOptimizer.PreallocateSpace(testFile, 1024 * 1024); // 1MB
                Debug.Log("预分配空间测试成功");

                // 测试文件系统缓存优化
                FileSystemOptimizer.OptimizeFileSystemCache();
                Debug.Log("文件系统缓存优化测试成功");

                // 清理测试文件
                if (System.IO.File.Exists(testFile))
                {
                    System.IO.File.Delete(testFile);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"文件系统优化测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试对象池功能
        /// </summary>
        [ContextMenu("测试对象池")]
        public void TestObjectPool()
        {
            Debug.Log("测试对象池功能");

            try
            {
                // 测试缓冲区池
                var buffer1 = ZipUtilsEx.ZipUtilsObjectPool.GetBuffer();
                var buffer2 = ZipUtilsEx.ZipUtilsObjectPool.GetBuffer();
                Debug.Log($"获取缓冲区: {buffer1.Length}, {buffer2.Length}");

                ZipUtilsEx.ZipUtilsObjectPool.ReturnBuffer(buffer1);
                ZipUtilsEx.ZipUtilsObjectPool.ReturnBuffer(buffer2);
                Debug.Log("缓冲区池测试成功");

                // 测试StringBuilder池
                var sb1 = ZipUtilsEx.ZipUtilsObjectPool.GetStringBuilder();
                var sb2 = ZipUtilsEx.ZipUtilsObjectPool.GetStringBuilder();
                sb1.Append("Test1");
                sb2.Append("Test2");
                Debug.Log($"StringBuilder测试: {sb1}, {sb2}");

                ZipUtilsEx.ZipUtilsObjectPool.ReturnStringBuilder(sb1);
                ZipUtilsEx.ZipUtilsObjectPool.ReturnStringBuilder(sb2);
                Debug.Log("StringBuilder池测试成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"对象池测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取APK路径测试
        /// </summary>
        [ContextMenu("测试APK路径获取")]
        public void TestGetApkPath()
        {
            string apkPath = ZipUtilsEx.GetApkPath();
            Debug.Log($"APK路径: {apkPath}");
            
            if (!string.IsNullOrEmpty(apkPath))
            {
                Debug.Log($"APK文件存在: {System.IO.File.Exists(apkPath)}");
            }
        }

        /// <summary>
        /// 性能基准测试
        /// </summary>
        [ContextMenu("运行性能基准测试")]
        public void RunPerformanceBenchmark()
        {
            if (string.IsNullOrEmpty(testZipPath))
            {
                Debug.LogError("请先设置测试ZIP文件路径");
                return;
            }

            StartCoroutine(PerformanceBenchmarkCoroutine());
        }

        private IEnumerator PerformanceBenchmarkCoroutine()
        {
            Debug.Log("=== 性能基准测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => { /* 静默处理 */ },
                OnPackFinishedCall = (success, message) => Debug.Log($"解压完成: {success}")
            };

            // 基准测试：原始方法
            var stopwatch1 = System.Diagnostics.Stopwatch.StartNew();
            yield return ZipUtilsEx.DecompressionCoroutine(testZipPath, testOutputPath + "_benchmark1", true, callback, 1);
            stopwatch1.Stop();
            Debug.Log($"原始方法耗时: {stopwatch1.Elapsed.TotalSeconds:F2}秒");

            yield return new WaitForSeconds(2f);

            // 基准测试：高性能方法
            var stopwatch2 = System.Diagnostics.Stopwatch.StartNew();
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                testZipPath, testOutputPath + "_benchmark2", true, callback, 8);
            stopwatch2.Stop();
            Debug.Log($"高性能方法耗时: {stopwatch2.Elapsed.TotalSeconds:F2}秒");

            // 计算性能提升
            double improvement = stopwatch1.Elapsed.TotalSeconds / stopwatch2.Elapsed.TotalSeconds;
            Debug.Log($"=== 性能提升: {improvement:F2}倍 ===");
        }
    }
}
