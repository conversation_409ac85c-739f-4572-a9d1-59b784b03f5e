using HotFix;
using Ionic.Zip;
using LitJson;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;
using static ZipUtils;

namespace HotFix
{
    public struct HotFixFileInfo
    {
        public string ZipServerPath;
        public int FileSize;
        public string MD5;
        public string FileName;
        public string FileLocalPath;
    }

    public class HotfixManager : MonoBehaviour
    {
        public enum HotFixProcessType
        {
            StartUpHotFix,
            WaitWork,
            RequestVersion,
            WaitRequestVersion,
            RequestVersionError,
            WaitUserConfirmationReapplyRequestVersion,
            DownLoadError,
            NoUpdates,
            PrepareForNormalUpdate,
            WaitUserConfirmationDownLoadNormalUpdate,
            DownLoadNormalUpdate,
            WaitDownLoadNormalUpdate,
            DownLoadOneFileFinish,
            HotFixUnpacking,
            WaitHotFixUnpacking,
            UnpackFinish,
            PromptForRestart,
            WaitUser<PERSON>onfirmationRestart,
            LoadDLL,
            WaitLoadDLL,
            EnterGame,
            RestartGame,
            CheckHotFixFailure,
            WaitUserConfirmationCheckNet,
            CopyAssets, //! 复制文件到外面
        }
        private HotFixProcessType m_curProcess = HotFixProcessType.NoUpdates;
        private float _waitRequestVersionTimer = 0;
        private float _waitUserConfirmationDownLoadTimer = 0;
        private bool _isNeedRestart = false;
        private const string RemoteVersionListFileName = "GameFrameworkVersion.dat";
        private int _curVersionNumber;
        private JsonData _hotFixResult;
        private HotFixFileInfo _serverZipInfo;
        private ZipCallback zipCallback = new ZipCallback();
        private bool IsNeedResetStartGame = false;
        private HashSet<string> NeedResetFile = new HashSet<string>();
        private HotFixWindow _hotFixWindow;
        public static HotfixManager Instance;
        private int NumberOfFailures = 0;
        private int MaxNumberOfFailures = 5;
        private string packageName = "";
        private void Awake()
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        void Start()
        {

#if UNITY_EDITOR
            UnityEngine.SceneManagement.SceneManager.LoadScene("SceneMain", UnityEngine.SceneManagement.LoadSceneMode.Single);
            return;
#endif
            //Debug.unityLogger.filterLogType = LogType.Exception;
            //! 另外定义了渠道号，覆盖本地的渠道号
            //! 可以在DLL中ChannelKeyLock锁定，不接受底层的渠道号
            if (GameLaunch.ChannelKey != "" && AppDefine.ChannelKeyLock == false && AppDefine.ChannelKey != "channelKey")
            {
                AppDefine.ChannelKey = GameLaunch.ChannelKey;
            }

            AppDefine.IsCheck = GameLaunch.IsCheck;

            m_curProcess = HotFixProcessType.StartUpHotFix;
            if (AppDefine.IsCheck)
            {
                m_curProcess = HotFixProcessType.NoUpdates;
            }
            //else
            {
                m_curProcess = HotFixProcessType.CopyAssets;
            }
            
            //! 从APK中解压文件到更新目录

            _hotFixWindow = GetComponentInChildren<HotFixWindow>();
            AddNeedResetFile("UnityGameFramework.Runtime");
            AddNeedResetFile("GameFramework");
            AddNeedResetFile("HotFixLogic");
            zipCallback.OnPostUnzipCall = OnPostZip;
            _hotFixWindow.SetSureCall(OnUserConfirmation);
            _hotFixWindow.SetCanceCall(OnUserCancellation);

#if UNITY_ANDROID
            // 创建回调
            ZipUtilsEx.ZipCallback callback = new ZipUtilsEx.ZipCallback
            {
                OnPreUnzipCall = (entry) =>
                {
                    // 示例：只提取assets目录下的文件
                    return entry.FileName.StartsWith("assets/");
                },
            
                OnPostUnzipCall = (entry) =>
                {
                    //Debug.Log($"已提取: {entry.FileName}");
                },
            
                OnProgressCallback = (progress) =>
                {
                    //Debug.Log($"提取进度: {progress:P2}");
                    if (_hotFixWindow)
                    {
                        _hotFixWindow.SetProgressTipText($"加载进度：{progress:P0}");
                        _hotFixWindow.SetProgress(progress);
                    }
                },
            
                OnPackFinishedCall = (success, message) =>
                {
                    Debug.Log(success ? $"APK提取成功: {message}" : $"APK提取失败: {message}");
                    m_curProcess = HotFixProcessType.StartUpHotFix;
                }
            };
                
            string filePath = Application.persistentDataPath;
            StartCoroutine(HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(ZipUtilsEx.GetApkPath(),
                filePath, true, callback, 4));

            _hotFixWindow.SetProgressbarRoothow(true);
#endif
        }

        public void DestoryHotFix()
        {
            Instance = null;
            GameObject.DestroyImmediate(this.gameObject);
        }
        private void OnUserCancellation()
        {
            m_curProcess = HotFixProcessType.RestartGame;
        }
        private void OnUserConfirmation()
        {
            if (m_curProcess == HotFixProcessType.WaitUserConfirmationDownLoadNormalUpdate)
            {
                m_curProcess = HotFixProcessType.DownLoadNormalUpdate;
            }
            else if (m_curProcess == HotFixProcessType.WaitUserConfirmationRestart)
            {
                m_curProcess = HotFixProcessType.RestartGame;
            }
            else if (m_curProcess == HotFixProcessType.WaitUserConfirmationCheckNet)
            {
                m_curProcess = HotFixProcessType.RequestVersion;

            }
        }
        private void AddNeedResetFile(string filename)
        {
            if (!NeedResetFile.Contains(filename))
            {
                NeedResetFile.Add(filename);
            }
            if (GameLaunch.AssemblyAlias.ContainsKey(filename))
            {
                if (!NeedResetFile.Contains(GameLaunch.AssemblyAlias[filename]))
                {
                    NeedResetFile.Add(GameLaunch.AssemblyAlias[filename]);
                }
            }
        }

        public void OnPostZip(ZipEntry _entry)
        {

            if (NeedResetFile.Contains(_entry.FileName))
            {
                IsNeedResetStartGame = true;
            }
        }

        public void OnFinished(bool _result)
        {
            if (_result)
            {
                m_curProcess = HotFixProcessType.UnpackFinish;
            }
            else
            {
                m_curProcess = HotFixProcessType.DownLoadNormalUpdate;

            }
            if (File.Exists(_serverZipInfo.FileLocalPath))
            {
                File.Delete(_serverZipInfo.FileLocalPath);
            }

        }
        private IEnumerator GoToMainScene()
        {
            yield return null;

            string scenePath = Application.persistentDataPath + "/" + GameLaunch.AssemblyAlias["SceneMain"];// GetSavePath() + " /" + m_hotFixDll;
            byte[] sceneByte = AesHelper.LoadDll(scenePath);

            AssetBundle ab = AssetBundle.LoadFromMemory(sceneByte);
            //  UnityEngine.SceneManagement.SceneManager.LoadScene("SceneMain", UnityEngine.SceneManagement.LoadSceneMode.Single);


            //ab.Unload(false);

        }

        private void LoadDll(string name)
        {
            string dllPath = GameLaunch.GetFistDllPath(name, GameLaunch.AssemblyAlias[name]);// GetSavePath() + "/" + m_hotFixDll;
            byte[] dllbytes = AesHelper.LoadDll(dllPath);
            RuntimeDebug.Log($"assets:{dllPath} length:{dllbytes.Length}");
            var gameAss = System.Reflection.Assembly.Load(dllbytes);
        }
        private string GetWebRequestPathWithPersistent(string asset)
        {
            var path = $"{Application.persistentDataPath}/{asset}";
            return path;
        }
        private string GetWebRequestPath(string asset)
        {
            var path = $"{Application.streamingAssetsPath}/{asset}";
            if (!path.Contains("://"))
            {
                path = "file://" + path;
            }

            return path;
        }
        void DecryptDll(string dllPath, byte[] byteArray)
        {
            using (MemoryStream ms = new MemoryStream(byteArray))
            {
                using (BinaryReader pak_br = new BinaryReader(ms))
                {
                    ulong _stamp = pak_br.ReadUInt64();
                    string _key = pak_br.ReadString();
                    int _discard1 = pak_br.ReadInt32();
                    int _Length = pak_br.ReadInt32();
                    byte[] dllbytes = pak_br.ReadBytes(_Length);
                    byte[] xorBytes = AesHelper.XORBytes(dllbytes, _key);
                    File.WriteAllBytes(dllPath, xorBytes);
                }
            }
        }
        private IEnumerator CopyFileTopersistentDataPath(string resPath, string targetpersistentPath, bool decrypt)
        {
            WWW www = new WWW(GetWebRequestPath(resPath));
            yield return www;
            if (www.isDone)
            {
                string targetPath = GetWebRequestPathWithPersistent(targetpersistentPath);
                if (decrypt)
                {
                    DecryptDll(targetPath, www.bytes);
                }
                else
                    File.WriteAllBytes(targetPath, www.bytes);
            }
            www = null;
        }


        private IEnumerator CopyMP4()
        {
         
            TextAsset temp = Resources.Load<TextAsset>(AppDefine.PasswordFileName);
            string ENCRYPT_KEY = temp != null ? temp.text : "";

            string realName = "VideoNameFile";

            if (!string.IsNullOrEmpty(ENCRYPT_KEY))
            {
                realName = GetMD5WithString(realName + ENCRYPT_KEY);
            }
            string nameHotFixPath = Path.Combine(Application.persistentDataPath, realName);
            if (!File.Exists(nameHotFixPath))
            {
                bool isError = false;
                byte[] bytes = null;
                string errorMessage = null;

                string privatepath = Path.Combine(Application.streamingAssetsPath, realName);
                //Debug.LogError("Video  CopyMP4  privatepath =  " + privatepath);

#if UNITY_IOS
                if (!File.Exists(privatepath))
                {
                    isError = true;
                } 
                bytes = File.ReadAllBytes(privatepath);
#else
                UnityWebRequest unityWebRequest = UnityWebRequest.Get(privatepath);
                yield return unityWebRequest.SendWebRequest();
                isError = unityWebRequest.result != UnityWebRequest.Result.Success;
                bytes = unityWebRequest.downloadHandler.data;
                errorMessage = isError ? unityWebRequest.error : null;
                unityWebRequest.Dispose();  
#endif                
                if (!isError)
                {
                    string mp4Path = Application.persistentDataPath + "/Video";
                    if (!Directory.Exists(mp4Path))
                    {
                        Directory.CreateDirectory(mp4Path);
                    }

                    File.WriteAllBytes(nameHotFixPath, bytes);
                    byte[] fileAllByte = AesHelper.LoadDll(nameHotFixPath);
                    string str = System.Text.Encoding.UTF8.GetString(fileAllByte);
                    string[] allName = str.Split('|');
                    for (int i = 0; i < allName.Length; i++)
                    {
                        allName[i] = allName[i].Replace("\r\n", "");
                        string[] fileName = allName[i].Split('#');
                        if (fileName.Length == 2)
                        {
                            string originalfileName = fileName[1];
                            string encryptName = fileName[0];
                            yield return CopyFileTopersistentDataPath(encryptName, "Video/" + originalfileName, true);
                        }
                       
                    }
                }
            }
            else
            {
                yield return null;
            }


        }
        string GetMD5WithString(string sDataIn)
        {
            string str = "";
            byte[] data = Encoding.GetEncoding("utf-8").GetBytes(sDataIn);
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] bytes = md5.ComputeHash(data);
            for (int i = 0; i < bytes.Length; i++)
            {
                str += bytes[i].ToString("x2");
            }
            return str;
        }
        private IEnumerator OnStartUpHotFix()
        {
            NumberOfFailures = 0;
            RuntimeDebug.Log("检查热更新");
            _hotFixWindow.SetProgressbarRoothow(true);
            _hotFixWindow.SetProgressBarSliderShow(false, 0);
            _hotFixWindow.SetProgressTipText("正在检测热更新...");
            bool isError = false;
            byte[] bytes = null;
            string errorMessage = null;
            DateTime startTime = DateTime.UtcNow;

            TextAsset temp = Resources.Load<TextAsset>(AppDefine.PasswordFileName);
            string ENCRYPT_KEY = temp != null ? temp.text : "";

            string realName = RemoteVersionListFileName;

            if (!string.IsNullOrEmpty(ENCRYPT_KEY))
            {
                realName = GetMD5WithString(RemoteVersionListFileName + ENCRYPT_KEY);
            }

            string path = Path.Combine(Application.persistentDataPath, realName);
            if (!File.Exists(path))
            {
                path = Path.Combine(Application.streamingAssetsPath, realName);
            }
            path = HotFixUtility.Path.GetRemotePath(path);
#if UNITY_5_4_OR_NEWER
            UnityWebRequest unityWebRequest = UnityWebRequest.Get(path);
#if UNITY_2017_2_OR_NEWER
            yield return unityWebRequest.SendWebRequest();
#else
                        yield return unityWebRequest.Send();
#endif

#if UNITY_2020_2_OR_NEWER
            isError = unityWebRequest.result != UnityWebRequest.Result.Success;
#elif UNITY_2017_1_OR_NEWER
                        isError = unityWebRequest.isNetworkError || unityWebRequest.isHttpError;
#else
                        isError = unityWebRequest.isError;
#endif
            bytes = unityWebRequest.downloadHandler.data;
            errorMessage = isError ? unityWebRequest.error : null;
            unityWebRequest.Dispose();
#else
                        WWW www = new WWW(fileUri);
                        yield return www;

                        isError = !string.IsNullOrEmpty(www.error);
                        bytes = www.bytes;
                        errorMessage = www.error;
                        www.Dispose();
#endif

            if (!isError)
            {
                MemoryStream memoryStream = new MemoryStream(bytes, false);
                PackageVersionList versionList = HotFixUtility.PackageVersionListDeserializeCallback_V2(memoryStream);
                string[] versionArray = versionList.ApplicableGameVersion.Split('.');
                if (versionArray.Length == 2)
                {
                    _curVersionNumber = ToInt(versionArray[0]) * 1000 + ToInt(versionArray[1]) * 100 + versionList.InternalResourceVersion;
                    m_curProcess = HotFixProcessType.RequestVersion;
                }
                else
                {
                    m_curProcess = HotFixProcessType.WaitHotFixUnpacking;
                }
            }
            else
            {
                m_curProcess = HotFixProcessType.WaitHotFixUnpacking;
            }
        }
        private int ToInt(string value)
        {
            try
            {
                return int.Parse(value);
            }
            catch (Exception)
            {

                return 0;
            }
        }
        
        string GetPackageNameCorrect()  
        {  
            using (AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))  
            {  
                using (AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity"))
                {
                    return currentActivity.Call<string>("getPackageName");  
                }  
            }  
        } 
        
        private IEnumerator RequestVersion()
        {
            _waitRequestVersionTimer = Time.realtimeSinceStartup;
            string platorm = "";
            if (Application.platform == RuntimePlatform.Android)
            {
                platorm = "Android";
            }
            else if (Application.platform == RuntimePlatform.IPhonePlayer)
            {
                platorm = "iOS";
            }
            else
            {
                platorm = "Android";
            }
#if UNITY_ANDROID
            if (this.packageName == "")
            {
                packageName = GetPackageNameCorrect();
            }
#else       
            if (this.packageName == "")
            {
                packageName = Application.identifier;
            }
#endif
            string phpPath = string.Format(AppDefine.ApplyHotFixVersionPath, AppDefine.HotFixUrl, AppDefine.ChannelKey, _curVersionNumber.ToString(), "project",platorm,packageName);
            Debug.LogError("hotfix  phpPath = " + phpPath);
            WWW www = new WWW(phpPath);
            yield return www;
            if (string.IsNullOrEmpty(www.error))
            {
                if (string.IsNullOrEmpty(www.text))
                {
                    m_curProcess = HotFixProcessType.NoUpdates;

                }
                else
                {
                    // m_curProcess = HotFixProcessType.RequestVersionError;
                    _hotFixResult = JsonMapper.ToObject(www.text);
                    int fileSize = JsonHelper.ReadInt(_hotFixResult, "fileSize", 0);
                    if (fileSize > 0)
                    {
                        // 
                        JsonData data = _hotFixResult["assets"];

                        string fileName = "";
                        foreach (var item in data.Keys)
                        {
                            fileName = item;
                            break;
                        }
                        string fileMD5 = JsonHelper.ReadString(data[fileName], "md5", "");
                        _serverZipInfo = new HotFixFileInfo();
                        _serverZipInfo.ZipServerPath = string.Format("{0}/{1}", JsonHelper.ReadString(_hotFixResult, "packageUrl", ""), fileName);
                        _serverZipInfo.FileSize = fileSize;
                        _serverZipInfo.MD5 = fileMD5;
                        _serverZipInfo.FileName = fileName;
                        _serverZipInfo.FileLocalPath = string.Format("{0}/{1}", Application.persistentDataPath, fileName);
                        if (File.Exists(_serverZipInfo.FileLocalPath))
                        {
                            m_curProcess = HotFixProcessType.DownLoadOneFileFinish;
                        }
                        else
                            m_curProcess = HotFixProcessType.PrepareForNormalUpdate;
                    }
                    else
                    {
                        m_curProcess = HotFixProcessType.NoUpdates;
                    }
                }

            }
            else
            {
                NumberOfFailures++;
                if (NumberOfFailures < MaxNumberOfFailures)
                {
                    m_curProcess = HotFixProcessType.RequestVersionError;
                }
                else
                {
                    NumberOfFailures = 0;
                    m_curProcess = HotFixProcessType.CheckHotFixFailure;
                }

                yield break;
            }
        }
        private void OnCheckHotFixFail()
        {
            _hotFixWindow.SetDialogMessageText("获取版本失败，请检查网络");
            _hotFixWindow.SetMessageDialogShow(true);
            //  _waitUserConfirmationDownLoadTimer = Time.realtimeSinceStartup;
            m_curProcess = HotFixProcessType.WaitUserConfirmationCheckNet;
        }
        private void OnUserConfirmationDownLoadNormalUpdate()
        {
            _hotFixWindow.SetDialogMessageText(string.Format("客户端有新的资源更新,共计{0},请问是否进行更新？", FileSizeToString()));
            _hotFixWindow.SetMessageDialogShow(true);
            _waitUserConfirmationDownLoadTimer = Time.realtimeSinceStartup;
            m_curProcess = HotFixProcessType.WaitUserConfirmationDownLoadNormalUpdate;
        }
        private void OnWaitUserConfirmationDownLoadNormalUpdate()
        {
            if (Time.realtimeSinceStartup - _waitUserConfirmationDownLoadTimer >= 60f)
            {
                m_curProcess = HotFixProcessType.DownLoadNormalUpdate;
            }
        }
        public string GetMD5HashFromFile(string fileName)
        {
            try
            {
                FileStream file = new FileStream(fileName, System.IO.FileMode.Open);
                MD5 md5 = new MD5CryptoServiceProvider();
                byte[] retVal = md5.ComputeHash(file);
                file.Close();
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < retVal.Length; i++)
                {
                    sb.Append(retVal[i].ToString("x2"));
                }
                return sb.ToString();
            }
            catch (Exception ex)
            {
                throw new Exception("GetMD5HashFromFile() fail,error:" + ex.Message);
            }
        }

        public void SetHotFixProgress(string tip, float value)
        {
            if (_hotFixWindow != null)
            {
                _hotFixWindow.SetProgress(value);
                _hotFixWindow.SetProgressbarRoothow(true);
                _hotFixWindow.SetProgressTipText(tip);
            }

        }

        private void OnDownLoadNormalUpdate()
        {
            SetHotFixProgress("正在为您准备资源包...", 0);
            if (File.Exists(_serverZipInfo.FileLocalPath))
            {
                m_curProcess = HotFixProcessType.DownLoadOneFileFinish;
            }
            else
            {
                m_curProcess = HotFixProcessType.WaitDownLoadNormalUpdate;
                DownLoadUtil.Get(gameObject, ReciveData, DownloadUpdateAPKEnd).DownLoadFile(_serverZipInfo.ZipServerPath, _serverZipInfo.FileLocalPath);
            }

        }


        private void OnDownLoadOneFileFinish()
        {
            bool isvalid = true;
            if (File.Exists(_serverZipInfo.FileLocalPath))
            {
                string md5 = GetMD5HashFromFile(_serverZipInfo.FileLocalPath);
                isvalid = md5 == _serverZipInfo.MD5;

            }
            if (isvalid)
            {
                m_curProcess = HotFixProcessType.HotFixUnpacking;
            }
            else
            {
                if (File.Exists(_serverZipInfo.FileLocalPath))
                {
                    File.Delete(_serverZipInfo.FileLocalPath);
                }
                m_curProcess = HotFixProcessType.DownLoadNormalUpdate;
            }
        }

        private void OnHotFixUnpacking()
        {
            if (File.Exists(_serverZipInfo.FileLocalPath))
            {
                m_curProcess = HotFixProcessType.WaitHotFixUnpacking;
                bool isfinish = ZipUtils.Decompression(_serverZipInfo.FileLocalPath, Application.persistentDataPath, true, zipCallback);
                OnFinished(isfinish);
            }
            else
            {
                m_curProcess = HotFixProcessType.DownLoadNormalUpdate;
            }

        }

        private string FileSizeToString()
        {
            float toKB = _serverZipInfo.FileSize / 1024f;
            float toMB = toKB / 1024f;

            string targetleng = "";
            if (toMB > 1)
            {
                targetleng = Math.Round(toMB) + "MB";
            }
            else
            {
                targetleng = Math.Round(toKB) + "KB";
            }
            return targetleng;
        }

        void ReciveData(float progress, float Speed, string ExpectDownloadTime, string CurDownloadFileSize)
        {
            _hotFixWindow.SetProgress(progress);
            _hotFixWindow.SetProgressTipText(string.Format("正在为您下载资源包...本次下载{0},速度{1}K/S,预计剩余时间{2}", FileSizeToString(), Speed.ToString(), ExpectDownloadTime));
            //_curDownLoadProgress = p;
            //_speed = s;
            //ExpectDownloadTime = t;
            //CurDownloadFileSize = f;
        }
        void DownloadUpdateAPKEnd(bool remove = true)
        {
            _hotFixWindow.SetProgressTipText("正在解压资源包...");
            m_curProcess = HotFixProcessType.DownLoadOneFileFinish;

        }

        private void OnPromptForRestart()
        {
            _hotFixWindow.SetDialogMessageText("本次更新需要重启生效，请问是否进行重启？");
            _hotFixWindow.SetMessageDialogShow(true);
        }
        private IEnumerator OnLoadDLL()
        {
            RuntimeDebug.Log("LoadDll");

            LoadDll("GameFramework");
            LoadDll("UnityGameFramework.Runtime");
            LoadDll("MainLogic");
            yield return CopyMP4();
            yield return GoToMainScene();
            m_curProcess = HotFixProcessType.EnterGame;
        }
        private void Update()
        {
            switch (m_curProcess)
            {
                case HotFixProcessType.StartUpHotFix:
                    m_curProcess = HotFixProcessType.WaitWork;
                    StartCoroutine(OnStartUpHotFix());
                    break;
                case HotFixProcessType.WaitWork:
                    break;
                case HotFixProcessType.RequestVersion:
                    m_curProcess = HotFixProcessType.WaitRequestVersion;
                    StartCoroutine(RequestVersion());
                    break;
                case HotFixProcessType.CheckHotFixFailure:
                    m_curProcess = HotFixProcessType.WaitWork;
                    OnCheckHotFixFail();
                    break;
                case HotFixProcessType.WaitRequestVersion:
                    if (Time.realtimeSinceStartup - _waitRequestVersionTimer >= 10f)
                    {
                        m_curProcess = HotFixProcessType.RequestVersion;
                    }
                    break;
                case HotFixProcessType.RequestVersionError:
                    m_curProcess = HotFixProcessType.RequestVersion;
                    break;
                case HotFixProcessType.WaitUserConfirmationReapplyRequestVersion:
                    break;
                case HotFixProcessType.DownLoadError:
                    break;
                case HotFixProcessType.NoUpdates:
                    m_curProcess = HotFixProcessType.LoadDLL;
                    break;
                case HotFixProcessType.PrepareForNormalUpdate:
                    m_curProcess = HotFixProcessType.WaitUserConfirmationDownLoadNormalUpdate;
                    OnUserConfirmationDownLoadNormalUpdate();
                    break;
                case HotFixProcessType.WaitUserConfirmationDownLoadNormalUpdate:
                    OnWaitUserConfirmationDownLoadNormalUpdate();
                    break;
                case HotFixProcessType.DownLoadNormalUpdate:

                    OnDownLoadNormalUpdate();
                    break;
                case HotFixProcessType.DownLoadOneFileFinish:
                    OnDownLoadOneFileFinish();
                    break;
                case HotFixProcessType.HotFixUnpacking:
                    OnHotFixUnpacking();
                    break;
                case HotFixProcessType.WaitHotFixUnpacking:
                    break;
                case HotFixProcessType.UnpackFinish:
                    {
                        if (_isNeedRestart)
                        {
                            m_curProcess = HotFixProcessType.PromptForRestart;
                            return;
                        }
                        m_curProcess = HotFixProcessType.LoadDLL;
                    }
                    break;
                case HotFixProcessType.PromptForRestart:
                    m_curProcess = HotFixProcessType.WaitUserConfirmationRestart;
                    OnPromptForRestart();
                    break;
                case HotFixProcessType.WaitUserConfirmationRestart:
                    break;
                case HotFixProcessType.LoadDLL:
                    StartCoroutine(OnLoadDLL());
                    m_curProcess = HotFixProcessType.WaitLoadDLL;
                    break;
                case HotFixProcessType.WaitLoadDLL:

                    break;
                case HotFixProcessType.EnterGame:
                    RuntimeDebug.Log("热更新结束");
                    UnityEngine.SceneManagement.SceneManager.LoadScene("SceneMain", UnityEngine.SceneManagement.LoadSceneMode.Single);
                    m_curProcess = HotFixProcessType.WaitLoadDLL;
                    break;
                case HotFixProcessType.RestartGame:
                    m_curProcess = HotFixProcessType.WaitWork;
                    AppDefine.ReStartAPP();
                    break;
                default:
                    break;
            }
        }
    }
}