# "One or more errors occurred" 异常修复指南

## 问题描述
`DecompressionCoroutineMemoryMapped`方法抛出异常：
```
DecompressionCoroutineMemoryMapped 发生异常: One or more errors occurred.
```

这是典型的`AggregateException`异常，通常发生在多线程任务执行时。

## 🔍 问题分析

### 1. AggregateException的特点
- 当多个Task中有一个或多个失败时抛出
- 包含一个或多个内部异常(`InnerExceptions`)
- 原始错误信息被包装，难以直接看到根本原因

### 2. 常见原因
- **文件访问权限问题**: 无法创建目录或写入文件
- **磁盘空间不足**: 解压过程中磁盘空间耗尽
- **文件路径问题**: 路径过长或包含非法字符
- **ZIP文件损坏**: 压缩文件本身有问题
- **内存不足**: 大文件处理时内存溢出
- **线程同步问题**: 多线程访问冲突

## 🔧 修复方案

### 1. 增强异常处理
已添加详细的异常信息记录：

```csharp
// 详细记录AggregateException的所有内部异常
catch (AggregateException aggEx)
{
    Debug.LogError($"聚合异常包含 {aggEx.InnerExceptions.Count} 个内部异常:");
    for (int i = 0; i < aggEx.InnerExceptions.Count; i++)
    {
        var innerEx = aggEx.InnerExceptions[i];
        Debug.LogError($"内部异常 {i + 1}: {innerEx.GetType().Name}: {innerEx.Message}");
    }
}
```

### 2. 安全的任务等待机制
替换`Task.WaitAll()`为更安全的逐个等待：

```csharp
// 逐个等待任务，记录成功和失败的数量
foreach (var task in tasks)
{
    try
    {
        task.Wait();
        completedTasks++;
    }
    catch (Exception ex)
    {
        failedTasks++;
        Debug.LogError($"任务失败: {ex.Message}");
    }
}
```

### 3. 文件级异常处理
在每个文件处理时添加异常捕获：

```csharp
foreach (var entry in batch)
{
    try
    {
        // 文件处理逻辑
    }
    catch (Exception entryEx)
    {
        Debug.LogError($"处理文件 {entry.FileName} 时发生异常: {entryEx.Message}");
        // 继续处理其他文件，不中断整个流程
    }
}
```

## 🧪 诊断步骤

### 1. 使用诊断工具
```csharp
var test = GetComponent<ZipUtilsTest>();
test.DiagnoseExceptionIssues(); // 详细诊断异常原因
```

### 2. 检查基本条件
- ✅ ZIP文件是否存在且可读
- ✅ 输出目录是否有写入权限
- ✅ 磁盘空间是否充足
- ✅ 文件路径是否合法

### 3. 逐步排查
1. **减少线程数**: 从8个线程减少到2个线程测试
2. **使用可靠版本**: 先用`DecompressionCoroutineReliable`测试
3. **检查日志**: 查看详细的异常信息

## 📋 常见解决方案

### 1. 权限问题
```csharp
// 确保输出目录存在且有写入权限
if (!Directory.Exists(outputDirectory))
{
    Directory.CreateDirectory(outputDirectory);
}
```

### 2. 磁盘空间问题
```csharp
// 检查磁盘空间
var drive = new DriveInfo(Path.GetPathRoot(outputDirectory));
if (drive.AvailableFreeSpace < estimatedSize)
{
    throw new InsufficientMemoryException("磁盘空间不足");
}
```

### 3. 路径长度问题
```csharp
// 检查路径长度
if (targetPath.Length > 260)
{
    Debug.LogWarning($"路径过长，跳过: {targetPath}");
    continue;
}
```

### 4. 内存问题
```csharp
// 减少缓冲区大小
const int LARGE_BUFFER_SIZE = 512 * 1024; // 从2MB减少到512KB
```

## 🚀 推荐使用流程

### 1. 首次使用
```csharp
// 使用诊断模式
test.DiagnoseExceptionIssues();
```

### 2. 生产环境
```csharp
// 使用较少线程数，更稳定
yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
    zipFilePath, destinationDirectory, true, callback, 4); // 4线程而不是8线程
```

### 3. 问题排查
```csharp
// 如果仍有问题，使用可靠版本
yield return HighPerformanceZipExtractor.DecompressionCoroutineReliable(
    zipFilePath, destinationDirectory, true, callback, 2);
```

## 📊 性能 vs 稳定性权衡

| 方法 | 线程数 | 稳定性 | 性能 | 推荐场景 |
|------|--------|--------|------|----------|
| DecompressionCoroutineReliable | 1 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 调试、问题排查 |
| DecompressionCoroutineMemoryMapped | 2-4 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 生产环境推荐 |
| DecompressionCoroutineMemoryMapped | 8+ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 高性能需求 |

## 🔍 日志分析

查看Unity Console中的详细日志：
- `任务完成统计`: 显示成功/失败的任务数量
- `内部异常`: 显示具体的错误原因
- `处理文件 xxx 时发生异常`: 显示具体哪个文件出错
- `异常类型` 和 `异常消息`: 显示详细的错误信息

通过这些信息可以准确定位问题根源并采取相应的解决措施。
