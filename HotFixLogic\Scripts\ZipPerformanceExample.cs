using System.Collections;
using UnityEngine;
using HotFix;

namespace HotFix
{
    /// <summary>
    /// ZIP解压性能优化示例和使用指南
    /// 针对1.6G资源从60s优化到20s的解决方案
    /// </summary>
    public class ZipPerformanceExample : MonoBehaviour
    {
        [Header("性能测试配置")]
        public string zipFilePath = "";
        public string destinationPath = "";
        public bool enablePerformanceTest = false;

        private void Start()
        {
            if (enablePerformanceTest && !string.IsNullOrEmpty(zipFilePath))
            {
                StartCoroutine(PerformanceComparisonTest());
            }
        }

        /// <summary>
        /// 性能对比测试 - 展示不同解压方法的性能差异
        /// </summary>
        private IEnumerator PerformanceComparisonTest()
        {
            Debug.Log("=== ZIP解压性能对比测试开始 ===");

            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = (progress) => Debug.Log($"解压进度: {progress:P2}"),
                OnPackFinishedCall = (success, message) => Debug.Log($"解压完成: {success}, {message}")
            };

            // 测试1: 原始方法
            Debug.Log("测试1: 原始单线程方法");
            var stopwatch1 = System.Diagnostics.Stopwatch.StartNew();
            yield return ZipUtilsEx.DecompressionCoroutine(zipFilePath, destinationPath + "_test1", true, callback, 1);
            stopwatch1.Stop();
            Debug.Log($"原始方法耗时: {stopwatch1.Elapsed.TotalSeconds:F2}秒");

            yield return new WaitForSeconds(1f);

            // 测试2: 优化的多线程方法
            Debug.Log("测试2: 高性能内存映射方法");
            var stopwatch2 = System.Diagnostics.Stopwatch.StartNew();
            yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                zipFilePath, destinationPath + "_test2", true, callback, 8);
            stopwatch2.Stop();
            Debug.Log($"高性能方法耗时: {stopwatch2.Elapsed.TotalSeconds:F2}秒");

            // 性能提升计算
            double improvement = stopwatch1.Elapsed.TotalSeconds / stopwatch2.Elapsed.TotalSeconds;
            Debug.Log($"=== 性能提升: {improvement:F2}倍 ===");
        }

        /// <summary>
        /// 推荐的最佳实践使用方法
        /// </summary>
        public void ExtractResourcesOptimized()
        {
            StartCoroutine(ExtractResourcesCoroutine());
        }

        private IEnumerator ExtractResourcesCoroutine()
        {
            var callback = new ZipUtilsEx.ZipCallback
            {
                OnProgressCallback = OnExtractionProgress,
                OnPackFinishedCall = OnExtractionFinished
            };

            // 对于Android APK资源，使用专门优化的方法
            if (Application.platform == RuntimePlatform.Android)
            {
                yield return HighPerformanceZipExtractor.DecompressApkAssetsUltraFast(
                    destinationPath, true, callback);
            }
            else
            {
                // 对于其他平台，使用高性能内存映射方法
                yield return HighPerformanceZipExtractor.DecompressionCoroutineMemoryMapped(
                    zipFilePath, destinationPath, true, callback, 8);
            }
        }

        private void OnExtractionProgress(float progress)
        {
            Debug.Log($"解压进度: {progress:P2}");
            // 更新UI进度条
        }

        private void OnExtractionFinished(bool success, string message)
        {
            if (success)
            {
                Debug.Log("资源解压成功!");
                // 继续游戏初始化流程
            }
            else
            {
                Debug.LogError($"资源解压失败: {message}");
                // 显示错误信息给用户
            }
        }
    }

    /// <summary>
    /// 性能优化配置类
    /// </summary>
    [System.Serializable]
    public class ZipPerformanceConfig
    {
        [Header("线程配置")]
        [Range(1, 16)]
        public int maxParallelThreads = 8;

        [Header("缓冲区配置")]
        [Range(64, 4096)]
        public int bufferSizeKB = 2048; // 2MB

        [Header("文件分类阈值")]
        [Range(1, 100)]
        public int largeFileMB = 50; // 50MB

        [Header("批处理配置")]
        [Range(1, 50)]
        public int batchSize = 5;

        [Header("优化选项")]
        public bool enableMemoryMapping = true;
        public bool enableFilePreallocation = true;
        public bool enableCacheOptimization = true;

        /// <summary>
        /// 获取优化后的线程数（基于CPU核心数）
        /// </summary>
        public int GetOptimalThreadCount()
        {
            int cpuCores = System.Environment.ProcessorCount;
            return Mathf.Clamp(maxParallelThreads, 1, cpuCores * 2);
        }

        /// <summary>
        /// 获取优化后的缓冲区大小
        /// </summary>
        public int GetOptimalBufferSize()
        {
            return bufferSizeKB * 1024;
        }

        /// <summary>
        /// 获取大文件阈值
        /// </summary>
        public long GetLargeFileThreshold()
        {
            return largeFileMB * 1024L * 1024L;
        }
    }
}
