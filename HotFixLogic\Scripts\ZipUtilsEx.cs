using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using ICSharpCode.SharpZipLib.Zip;

namespace HotFixLogic
{
    /// <summary>
    /// ZIP工具扩展类 - 提供高性能ZIP解压功能
    /// </summary>
    public static class ZipUtilsEx
    {
        // ZIP回调接口
        public class ZipCallback
        {
            public System.Action<float> OnProgressCallback;
            public System.Func<ZipEntry, bool> OnPreUnzipCall;
            public System.Action<ZipEntry> OnPostUnzipCall;
            public System.Action<bool, string> OnPackFinishedCall;
        }

        // 获取Android APK路径
        public static string GetApkPath()
        {
            if (Application.platform == RuntimePlatform.Android)
            {
                return Application.dataPath;
            }
            return string.Empty;
        }

        // 预过滤ZIP条目 - 优化版本
        public static List<ZipEntry> PreFilterEntriesOptimized(ZipFile zipFile)
        {
            var filteredEntries = new List<ZipEntry>();
            foreach (ZipEntry entry in zipFile)
            {
                if (!entry.IsDirectory && !string.IsNullOrEmpty(entry.FileName))
                {
                    filteredEntries.Add(entry);
                }
            }
            return filteredEntries;
        }

        // 预创建目录结构 - 优化版本
        public static void PreCreateDirectoriesOptimized(List<ZipEntry> entries, string destinationDirectory, StringBuilder pathBuilder)
        {
            var directories = new HashSet<string>();

            foreach (var entry in entries)
            {
                if (!entry.IsDirectory)
                {
                    pathBuilder.Clear();
                    pathBuilder.Append(destinationDirectory);
                    pathBuilder.Append(Path.DirectorySeparatorChar);
                    pathBuilder.Append(entry.FileName.Replace('/', Path.DirectorySeparatorChar));

                    string directoryPath = Path.GetDirectoryName(pathBuilder.ToString());
                    if (!string.IsNullOrEmpty(directoryPath) && directories.Add(directoryPath))
                    {
                        Directory.CreateDirectory(directoryPath);
                    }
                }
            }
        }

        // 构建目标路径 - 优化版本
        public static string BuildTargetPathOptimized(string fileName, string destinationDirectory, StringBuilder pathBuilder)
        {
            pathBuilder.Clear();
            pathBuilder.Append(destinationDirectory);
            pathBuilder.Append(Path.DirectorySeparatorChar);
            pathBuilder.Append(fileName.Replace('/', Path.DirectorySeparatorChar));
            return pathBuilder.ToString();
        }
    }

    /// <summary>
    /// 🚀 专门的APK资源释放接口类
    /// 针对大规模文件处理优化（7000+文件，1GB+总大小）
    /// </summary>
    public static class ApkAssetsExtractor
    {
        // 性能配置常量
        private const int ULTRA_BUFFER_SIZE = 4 * 1024 * 1024; // 4MB超大缓冲区
        private const int BATCH_YIELD_SIZE = 20; // 每处理20个文件让出一帧
        private const long LARGE_FILE_THRESHOLD = 10 * 1024 * 1024; // 10MB大文件阈值
        private const int PATH_BUILDER_CAPACITY = 512; // 路径构建器容量

        // 支持的assets路径模式
        private static readonly string[] SUPPORTED_ASSET_PATTERNS = {
            "assets/", // assets根目录文件
            "assets/Video/" // assets/Video目录文件
        };

        /// <summary>
        /// 🎯 主要接口：从APK中释放assets资源到指定目录
        /// </summary>
        /// <param name="targetDirectory">目标释放目录</param>
        /// <param name="overwrite">是否覆盖已存在文件</param>
        /// <param name="callback">进度和状态回调</param>
        /// <returns>协程枚举器</returns>
        public static IEnumerator ExtractAssetsFromApk(string targetDirectory,
            bool overwrite = true, ZipUtilsEx.ZipCallback callback = null)
        {
            // Android平台检查
            if (Application.platform != RuntimePlatform.Android)
            {
                callback?.OnPackFinishedCall?.Invoke(false, "错误: APK资源释放仅适用于Android平台");
                yield break;
            }

            // 获取APK路径
            string apkPath = ZipUtilsEx.GetApkPath();
            if (string.IsNullOrEmpty(apkPath) || !File.Exists(apkPath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 无法找到APK文件 - {apkPath}");
                yield break;
            }

            Debug.Log($"🚀 开始APK资源释放: {apkPath} -> {targetDirectory}");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 调用核心释放逻辑
            yield return ExtractAssetsFromZip(apkPath, targetDirectory, overwrite, callback);

            stopwatch.Stop();
            Debug.Log($"🎯 APK资源释放完成，总耗时: {stopwatch.Elapsed.TotalSeconds:F2}秒");
        }

        /// <summary>
        /// 🎯 核心接口：从ZIP文件中释放assets资源
        /// </summary>
        /// <param name="zipFilePath">ZIP文件路径</param>
        /// <param name="targetDirectory">目标释放目录</param>
        /// <param name="overwrite">是否覆盖已存在文件</param>
        /// <param name="callback">进度和状态回调</param>
        /// <returns>协程枚举器</returns>
        public static IEnumerator ExtractAssetsFromZip(string zipFilePath, string targetDirectory,
            bool overwrite = true, ZipUtilsEx.ZipCallback callback = null)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: ZIP文件不存在 - {zipFilePath}");
                yield break;
            }

            Debug.Log($"📦 开始assets资源释放: {zipFilePath}");
            Debug.Log($"📁 目标目录: {targetDirectory}");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                using (var zipFile = new ZipFile(zipFilePath))
                {
                    // 🔍 第一阶段：智能预过滤assets文件
                    var assetsEntries = PreFilterAssetsEntries(zipFile);

                    if (assetsEntries.Count == 0)
                    {
                        callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的assets文件");
                        yield break;
                    }

                    Debug.Log($"📊 找到 {assetsEntries.Count} 个assets文件需要释放");

                    // 🏗️ 第二阶段：预创建目标目录结构
                    PreCreateAssetsDirectories(assetsEntries, targetDirectory);

                    // ⚡ 第三阶段：高性能批量释放
                    yield return PerformHighSpeedExtraction(assetsEntries, targetDirectory, overwrite, callback);
                }

                stopwatch.Stop();
                Debug.Log($"✅ assets资源释放成功完成，耗时: {stopwatch.Elapsed.TotalSeconds:F2}秒");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Debug.LogError($"❌ assets资源释放失败: {ex.Message}");
                callback?.OnPackFinishedCall?.Invoke(false, $"释放失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 🔍 智能预过滤assets文件
        /// 只选择符合条件的assets路径文件
        /// </summary>
        /// <param name="zipFile">ZIP文件对象</param>
        /// <returns>过滤后的assets条目列表</returns>
        private static List<AssetsEntry> PreFilterAssetsEntries(ZipFile zipFile)
        {
            var assetsEntries = new List<AssetsEntry>();
            var pathBuilder = new StringBuilder(PATH_BUILDER_CAPACITY);

            foreach (ZipEntry entry in zipFile)
            {
                if (entry.IsDirectory || string.IsNullOrEmpty(entry.FileName))
                    continue;

                // 检查是否匹配支持的assets路径模式
                var mappedPath = GetAssetsMappedPath(entry.FileName, pathBuilder);
                if (!string.IsNullOrEmpty(mappedPath))
                {
                    assetsEntries.Add(new AssetsEntry
                    {
                        ZipEntry = entry,
                        OriginalPath = entry.FileName,
                        MappedPath = mappedPath,
                        FileSize = entry.UncompressedSize
                    });
                }
            }

            // 🚀 性能优化：按文件大小排序，大文件优先处理
            assetsEntries.Sort((a, b) => b.FileSize.CompareTo(a.FileSize));

            Debug.Log($"🔍 预过滤完成: 从ZIP中找到 {assetsEntries.Count} 个符合条件的assets文件");
            return assetsEntries;
        }

        /// <summary>
        /// 🗺️ 获取assets文件的目标映射路径
        /// assets/* -> /*
        /// assets/Video/* -> /Video/*
        /// </summary>
        /// <param name="originalPath">原始ZIP内路径</param>
        /// <param name="pathBuilder">路径构建器</param>
        /// <returns>映射后的目标路径，如果不匹配则返回null</returns>
        private static string GetAssetsMappedPath(string originalPath, StringBuilder pathBuilder)
        {
            // 检查 assets/ 根目录文件（不包括子目录）
            if (originalPath.StartsWith("assets/") && !originalPath.StartsWith("assets/Video/"))
            {
                // 检查是否是根目录文件（不包含更多的/）
                string relativePath = originalPath.Substring(7); // 移除 "assets/"
                if (!relativePath.Contains('/'))
                {
                    return relativePath; // 直接映射到根目录
                }
            }

            // 检查 assets/Video/ 目录文件（不包括子目录）
            if (originalPath.StartsWith("assets/Video/"))
            {
                string relativePath = originalPath.Substring(13); // 移除 "assets/Video/"
                if (!relativePath.Contains('/'))
                {
                    pathBuilder.Clear();
                    pathBuilder.Append("Video/");
                    pathBuilder.Append(relativePath);
                    return pathBuilder.ToString();
                }
            }

            return null; // 不匹配任何模式
        }

        /// <summary>
        /// 🏗️ 预创建assets目标目录结构
        /// </summary>
        /// <param name="assetsEntries">assets条目列表</param>
        /// <param name="targetDirectory">目标根目录</param>
        private static void PreCreateAssetsDirectories(List<AssetsEntry> assetsEntries, string targetDirectory)
        {
            var directories = new HashSet<string>();
            var pathBuilder = new StringBuilder(PATH_BUILDER_CAPACITY);

            // 确保目标根目录存在
            if (!Directory.Exists(targetDirectory))
            {
                Directory.CreateDirectory(targetDirectory);
            }

            foreach (var assetsEntry in assetsEntries)
            {
                pathBuilder.Clear();
                pathBuilder.Append(targetDirectory);
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append(assetsEntry.MappedPath.Replace('/', Path.DirectorySeparatorChar));

                string directoryPath = Path.GetDirectoryName(pathBuilder.ToString());
                if (!string.IsNullOrEmpty(directoryPath) && directories.Add(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }
            }

            Debug.Log($"🏗️ 预创建了 {directories.Count} 个目录");
        }

        /// <summary>
        /// ⚡ 高性能批量释放assets文件
        /// </summary>
        /// <param name="assetsEntries">assets条目列表</param>
        /// <param name="targetDirectory">目标目录</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="callback">回调</param>
        /// <returns>协程枚举器</returns>
        private static IEnumerator PerformHighSpeedExtraction(List<AssetsEntry> assetsEntries,
            string targetDirectory, bool overwrite, ZipUtilsEx.ZipCallback callback)
        {
            // 预分配超大缓冲区
            byte[] ultraBuffer = new byte[ULTRA_BUFFER_SIZE];
            var pathBuilder = new StringBuilder(PATH_BUILDER_CAPACITY);

            int totalEntries = assetsEntries.Count;
            int processedEntries = 0;
            int successCount = 0;
            int skipCount = 0;
            long totalBytesProcessed = 0;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            for (int i = 0; i < assetsEntries.Count; i++)
            {
                var assetsEntry = assetsEntries[i];
                var entry = assetsEntry.ZipEntry;

                try
                {
                    // 构建目标文件路径
                    pathBuilder.Clear();
                    pathBuilder.Append(targetDirectory);
                    pathBuilder.Append(Path.DirectorySeparatorChar);
                    pathBuilder.Append(assetsEntry.MappedPath.Replace('/', Path.DirectorySeparatorChar));
                    string targetPath = pathBuilder.ToString();

                    // 预处理回调
                    bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                    if (shouldProcess)
                    {
                        bool extractSuccess = false;

                        // 根据文件大小选择最优提取策略
                        if (entry.UncompressedSize > LARGE_FILE_THRESHOLD)
                        {
                            extractSuccess = ExtractLargeAssetFile(entry, targetPath, ultraBuffer, overwrite);
                        }
                        else
                        {
                            extractSuccess = ExtractSmallAssetFile(entry, targetPath, ultraBuffer, overwrite);
                        }

                        // 如果常规方法失败，尝试安全提取策略
                        if (!extractSuccess)
                        {
                            Debug.LogWarning($"⚠️ 常规提取失败，尝试安全策略: {assetsEntry.OriginalPath}");
                            extractSuccess = SafeExtractAssetFile(entry, targetPath, ultraBuffer, overwrite);
                        }

                        if (extractSuccess)
                        {
                            successCount++;
                            totalBytesProcessed += entry.UncompressedSize;
                            callback?.OnPostUnzipCall?.Invoke(entry);
                        }
                        else
                        {
                            Debug.LogError($"❌ 所有提取策略都失败: {assetsEntry.OriginalPath}");
                            skipCount++;
                        }
                    }
                    else
                    {
                        skipCount++;
                    }

                    processedEntries++;

                    // 更新进度
                    float progress = (float)processedEntries / totalEntries;
                    callback?.OnProgressCallback?.Invoke(progress);

                    // 定期让出控制权，保持UI响应
                    if (i % BATCH_YIELD_SIZE == 0)
                    {
                        yield return null;
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"❌ 处理assets文件失败: {assetsEntry.OriginalPath}, 错误: {ex.Message}");
                    processedEntries++;
                    skipCount++;
                }
            }

            stopwatch.Stop();
            double throughput = totalBytesProcessed / 1024.0 / 1024.0 / stopwatch.Elapsed.TotalSeconds;

            // 📊 性能统计报告
            Debug.Log($"⚡ 高性能assets释放完成:");
            Debug.Log($"   耗时: {stopwatch.Elapsed.TotalSeconds:F2}秒");
            Debug.Log($"   成功: {successCount}, 跳过: {skipCount}, 总计: {totalEntries}");
            Debug.Log($"   处理数据: {totalBytesProcessed / 1024.0 / 1024.0:F2}MB");
            Debug.Log($"   吞吐量: {throughput:F2}MB/s");

            callback?.OnProgressCallback?.Invoke(1.0f);

            if (skipCount == 0)
            {
                callback?.OnPackFinishedCall?.Invoke(true,
                    $"assets释放成功，处理了 {successCount} 个文件，吞吐量 {throughput:F2}MB/s");
            }
            else
            {
                callback?.OnPackFinishedCall?.Invoke(true,
                    $"assets释放完成，成功 {successCount} 个，跳过 {skipCount} 个文件");
            }
        }

        /// <summary>
        /// 🚀 高性能大文件assets提取
        /// </summary>
        /// <param name="entry">ZIP条目</param>
        /// <param name="targetPath">目标路径</param>
        /// <param name="buffer">缓冲区</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        private static bool ExtractLargeAssetFile(ZipEntry entry, string targetPath, byte[] buffer, bool overwrite)
        {
            if (!overwrite && File.Exists(targetPath))
                return true;

            try
            {
                long expectedSize = entry.UncompressedSize;
                long totalBytesRead = 0;

                using (var entryStream = entry.OpenReader())
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write,
                       FileShare.None, buffer.Length, FileOptions.SequentialScan | FileOptions.WriteThrough))
                {
                    // 预设文件大小以减少磁盘碎片
                    if (expectedSize > 0)
                    {
                        fileStream.SetLength(expectedSize);
                    }

                    int bytesRead;
                    while ((bytesRead = entryStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;

                        // 防止无限循环
                        if (totalBytesRead > expectedSize * 2)
                        {
                            throw new InvalidDataException($"读取数据量异常: {totalBytesRead} > {expectedSize * 2}");
                        }
                    }
                }

                // 验证文件大小
                if (expectedSize > 0 && totalBytesRead != expectedSize)
                {
                    Debug.LogError($"大文件大小不匹配: {entry.FileName}, 期望: {expectedSize}, 实际: {totalBytesRead}");
                    File.Delete(targetPath);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"大文件提取失败: {entry.FileName}, 错误: {ex.Message}");
                CleanupFailedFile(targetPath);
                return false;
            }
        }

        /// <summary>
        /// ⚡ 高性能小文件assets提取
        /// </summary>
        /// <param name="entry">ZIP条目</param>
        /// <param name="targetPath">目标路径</param>
        /// <param name="buffer">缓冲区</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        private static bool ExtractSmallAssetFile(ZipEntry entry, string targetPath, byte[] buffer, bool overwrite)
        {
            if (!overwrite && File.Exists(targetPath))
                return true;

            try
            {
                long expectedSize = entry.UncompressedSize;
                long totalBytesRead = 0;

                // 小文件使用较小的缓冲区以减少内存占用
                int smallBufferSize = Math.Min(buffer.Length, (int)Math.Max(expectedSize, 64 * 1024));

                using (var entryStream = entry.OpenReader())
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write,
                       FileShare.None, smallBufferSize, FileOptions.SequentialScan))
                {
                    int bytesRead;
                    while ((bytesRead = entryStream.Read(buffer, 0, smallBufferSize)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;

                        // 防止无限循环
                        if (totalBytesRead > expectedSize * 2)
                        {
                            throw new InvalidDataException($"读取数据量异常: {totalBytesRead} > {expectedSize * 2}");
                        }
                    }
                }

                // 验证文件大小
                if (expectedSize > 0 && totalBytesRead != expectedSize)
                {
                    Debug.LogError($"小文件大小不匹配: {entry.FileName}, 期望: {expectedSize}, 实际: {totalBytesRead}");
                    File.Delete(targetPath);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"小文件提取失败: {entry.FileName}, 错误: {ex.Message}");
                CleanupFailedFile(targetPath);
                return false;
            }
        }

        /// <summary>
        /// 🛡️ 安全的assets文件提取 - 处理损坏文件
        /// </summary>
        /// <param name="entry">ZIP条目</param>
        /// <param name="targetPath">目标路径</param>
        /// <param name="buffer">缓冲区</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        private static bool SafeExtractAssetFile(ZipEntry entry, string targetPath, byte[] buffer, bool overwrite)
        {
            if (!overwrite && File.Exists(targetPath))
                return true;

            // 尝试多种提取策略
            var strategies = new List<System.Func<bool>>
            {
                () => TryExtractWithRetry(entry, targetPath, buffer, 3), // 重试策略
                () => TryExtractWithSmallBuffer(entry, targetPath, 8192), // 小缓冲区策略
                () => TryExtractByteByByte(entry, targetPath), // 逐字节策略（最后手段）
            };

            foreach (var strategy in strategies)
            {
                try
                {
                    if (strategy())
                    {
                        Debug.Log($"🛡️ 安全提取成功: {entry.FileName}");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"提取策略失败: {entry.FileName}, 错误: {ex.Message}");
                }
            }

            Debug.LogError($"❌ 所有安全提取策略都失败: {entry.FileName}");
            return false;
        }

        /// <summary>
        /// 🔄 重试策略
        /// </summary>
        private static bool TryExtractWithRetry(ZipEntry entry, string targetPath, byte[] buffer, int maxRetries)
        {
            for (int retry = 0; retry < maxRetries; retry++)
            {
                try
                {
                    return ExtractLargeAssetFile(entry, targetPath, buffer, true);
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"重试 {retry + 1}/{maxRetries} 失败: {entry.FileName}, 错误: {ex.Message}");
                    CleanupFailedFile(targetPath);

                    if (retry < maxRetries - 1)
                    {
                        Thread.Sleep(100); // 短暂延迟
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 📦 小缓冲区策略
        /// </summary>
        private static bool TryExtractWithSmallBuffer(ZipEntry entry, string targetPath, int bufferSize)
        {
            try
            {
                long expectedSize = entry.UncompressedSize;
                long totalBytesRead = 0;
                byte[] smallBuffer = new byte[bufferSize];

                using (var entryStream = entry.OpenReader())
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write))
                {
                    int bytesRead;
                    while ((bytesRead = entryStream.Read(smallBuffer, 0, smallBuffer.Length)) > 0)
                    {
                        fileStream.Write(smallBuffer, 0, bytesRead);
                        totalBytesRead += bytesRead;

                        // 防止无限循环
                        if (totalBytesRead > expectedSize * 3)
                        {
                            throw new InvalidDataException("数据量异常");
                        }
                    }
                }

                // 验证大小
                if (expectedSize > 0 && totalBytesRead != expectedSize)
                {
                    Debug.LogWarning($"小缓冲区策略大小不匹配: {entry.FileName}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"小缓冲区策略失败: {entry.FileName}, 错误: {ex.Message}");
                CleanupFailedFile(targetPath);
                return false;
            }
        }

        /// <summary>
        /// 🐌 逐字节策略（最后手段）
        /// </summary>
        private static bool TryExtractByteByByte(ZipEntry entry, string targetPath)
        {
            try
            {
                long expectedSize = entry.UncompressedSize;
                long totalBytesRead = 0;
                const long MAX_BYTE_BY_BYTE_SIZE = 1024 * 1024; // 1MB限制

                // 只对小文件使用逐字节策略
                if (expectedSize > MAX_BYTE_BY_BYTE_SIZE)
                {
                    Debug.LogWarning($"文件过大，跳过逐字节策略: {entry.FileName}");
                    return false;
                }

                using (var entryStream = entry.OpenReader())
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write))
                {
                    int byteValue;
                    while ((byteValue = entryStream.ReadByte()) != -1)
                    {
                        fileStream.WriteByte((byte)byteValue);
                        totalBytesRead++;

                        // 防止无限循环
                        if (totalBytesRead > expectedSize * 2)
                        {
                            throw new InvalidDataException("逐字节读取数据量异常");
                        }
                    }
                }

                Debug.Log($"🐌 逐字节策略完成: {entry.FileName}, 读取: {totalBytesRead} bytes");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"逐字节策略失败: {entry.FileName}, 错误: {ex.Message}");
                CleanupFailedFile(targetPath);
                return false;
            }
        }

        /// <summary>
        /// 🧹 清理失败的文件
        /// </summary>
        private static void CleanupFailedFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    Debug.Log($"🧹 已清理失败的文件: {filePath}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"清理失败文件时发生错误: {filePath}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 📊 获取assets释放统计信息
        /// </summary>
        /// <param name="zipFilePath">ZIP文件路径</param>
        /// <returns>统计信息</returns>
        public static AssetsStatistics GetAssetsStatistics(string zipFilePath)
        {
            var statistics = new AssetsStatistics();

            try
            {
                using (var zipFile = new ZipFile(zipFilePath))
                {
                    var assetsEntries = PreFilterAssetsEntries(zipFile);

                    statistics.TotalAssetsFiles = assetsEntries.Count;
                    statistics.TotalAssetsSize = assetsEntries.Sum(e => e.FileSize);
                    statistics.RootAssetsFiles = assetsEntries.Count(e => !e.MappedPath.Contains('/'));
                    statistics.VideoAssetsFiles = assetsEntries.Count(e => e.MappedPath.StartsWith("Video/"));
                    statistics.LargeFiles = assetsEntries.Count(e => e.FileSize > LARGE_FILE_THRESHOLD);
                    statistics.SmallFiles = assetsEntries.Count(e => e.FileSize <= LARGE_FILE_THRESHOLD);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"获取assets统计信息失败: {ex.Message}");
            }

            return statistics;
        }
    }

    /// <summary>
    /// 📁 Assets条目数据结构
    /// </summary>
    public class AssetsEntry
    {
        public ZipEntry ZipEntry { get; set; }
        public string OriginalPath { get; set; }
        public string MappedPath { get; set; }
        public long FileSize { get; set; }
    }

    /// <summary>
    /// 📊 Assets释放统计信息
    /// </summary>
    public class AssetsStatistics
    {
        public int TotalAssetsFiles { get; set; }
        public long TotalAssetsSize { get; set; }
        public int RootAssetsFiles { get; set; }
        public int VideoAssetsFiles { get; set; }
        public int LargeFiles { get; set; }
        public int SmallFiles { get; set; }

        public override string ToString()
        {
            return $"Assets统计: 总文件 {TotalAssetsFiles} 个, " +
                   $"总大小 {TotalAssetsSize / 1024.0 / 1024.0:F2}MB, " +
                   $"根目录 {RootAssetsFiles} 个, " +
                   $"Video目录 {VideoAssetsFiles} 个, " +
                   $"大文件 {LargeFiles} 个, " +
                   $"小文件 {SmallFiles} 个";
        }
    }
}