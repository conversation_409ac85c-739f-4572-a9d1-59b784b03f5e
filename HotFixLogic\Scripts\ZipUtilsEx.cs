using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using Ionic.Zip;

namespace HotFix
{
    public static class ZipUtilsEx
    {
        // 解压回调接口
        public class ZipCallback
        {
            public Func<ZipEntry, bool> OnPreUnzipCall;
            public Action<ZipEntry> OnPostUnzipCall;
            public Action<float> OnProgressCallback;
            public Action<bool, string> OnPackFinishedCall;
        }

        // 异步解压协程 - 支持指定目录过滤
        public static IEnumerator DecompressionCoroutine(string zipFilePath, string destinationDirectory,
            bool overwrite = true, ZipCallback callback = null, int maxParallelThreads = 4)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }


            using (ZipFile zipFile = new ZipFile(zipFilePath))
            {
                // 筛选符合条件的条目
                List<ZipEntry> filteredEntries = new List<ZipEntry>();

                foreach (ZipEntry entry in zipFile)
                {
                    string fileName = entry.FileName.Replace('\\', '/');

                    if (fileName.StartsWith("assets/"))
                    {
                        if (fileName.IndexOf('/', 7) == -1 || fileName.StartsWith("assets/Video/"))
                        {
                            filteredEntries.Add(entry);
                        }
                    }
                }

                int totalEntries = filteredEntries.Count;
                if (totalEntries == 0)
                {
                    callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                    yield break;
                }

                // 预创建Video目录
                string videoPath = Path.Combine(destinationDirectory, "Video");
                if (!Directory.Exists(videoPath))
                {
                    Directory.CreateDirectory(videoPath);
                }

                int processedEntries = 0;
                bool anyError = false;
                string errorMessage = string.Empty;

                // 使用队列来管理任务
                Queue<ZipEntry> entryQueue = new Queue<ZipEntry>(filteredEntries);

                while (entryQueue.Count > 0 && !anyError)
                {
                    var currentBatch = new List<ZipEntry>();
                    int batchSize = Math.Min(maxParallelThreads, entryQueue.Count);

                    for (int i = 0; i < batchSize; i++)
                    {
                        currentBatch.Add(entryQueue.Dequeue());
                    }

                    // 处理当前批次
                    foreach (var entry in currentBatch)
                    {
                        try
                        {
                            bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                            if (shouldProcess && !entry.IsDirectory)
                            {
                                string fileName = entry.FileName.Replace('\\', '/');
                                string relativePath;

                                // 正确处理路径：移除assets/前缀，直接解压到目标目录
                                if (fileName.StartsWith("assets/Video/"))
                                {
                                    // assets/Video/xxx.mp4 -> Video/xxx.mp4
                                    relativePath = fileName.Substring(7); // 移除"assets/"前缀
                                }
                                else if (fileName.StartsWith("assets/"))
                                {
                                    // assets/xxx -> xxx (直接到根目录)
                                    relativePath = fileName.Substring(7); // 移除"assets/"前缀
                                }
                                else
                                {
                                    relativePath = fileName;
                                }

                                if (!string.IsNullOrEmpty(relativePath))
                                {
                                    string targetPath = Path.Combine(destinationDirectory, relativePath);
                                    string directory = Path.GetDirectoryName(targetPath);

                                    if (!Directory.Exists(directory))
                                    {
                                        Directory.CreateDirectory(directory);
                                    }

                                    // 直接解压到目标路径，而不是让Extract自动创建assets目录
                                    using (var entryStream = entry.OpenReader())
                                    {
                                        using (var fileStream = File.Create(targetPath))
                                        {
                                            entryStream.CopyTo(fileStream);
                                        }
                                    }

                                    //Debug.Log($"解压文件: {entry.FileName} -> {relativePath}");
                                    callback?.OnPostUnzipCall?.Invoke(entry);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            anyError = true;
                            errorMessage = ex.Message;
                            Debug.LogError($"解压文件 {entry.FileName} 时发生错误: {ex.Message}");
                            break;
                        }

                        processedEntries++;
                        float progress = (float)processedEntries / totalEntries;
                        callback?.OnProgressCallback?.Invoke(progress);
                    }

                    yield return null; // 让出控制权给Unity主线程
                }

                if (anyError)
                {
                    callback?.OnPackFinishedCall?.Invoke(false, $"解压过程中发生错误: {errorMessage}");
                }
                else
                {
                    callback?.OnPackFinishedCall?.Invoke(true, "解压成功");
                }
            }
        }


        // 异步解压APK中的assets目录和assets/Video目录文件
        public static IEnumerator DecompressApkAssetsCoroutine(string destinationDirectory,
            bool overwrite = true, ZipCallback callback = null, int maxParallelThreads = 4)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                callback?.OnPackFinishedCall?.Invoke(false, "错误: 此方法仅适用于Android平台");
                yield break;
            }

            string apkPath = GetApkPath();
            if (string.IsNullOrEmpty(apkPath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, "错误: 无法获取APK路径");
                yield break;
            }

            Debug.Log($"开始解压APK中的assets目录: {apkPath}");

            System.Diagnostics.Stopwatch stopwatch = System.Diagnostics.Stopwatch.StartNew();

            yield return DecompressionCoroutine(
                apkPath,
                destinationDirectory,
                overwrite,
                callback,
                maxParallelThreads);

            stopwatch.Stop();
            string timeMessage = $"耗时: {stopwatch.Elapsed.TotalSeconds:F2}秒";
            Debug.Log($"assets目录解压完成，{timeMessage}");
        }

        // 获取APK文件路径
        public static string GetApkPath()
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                Debug.LogWarning("GetApkPath() 仅在Android平台上有效");
                return string.Empty;
            }

            try
            {
                string path = Application.streamingAssetsPath;

                if (path.StartsWith("jar:file://"))
                {
                    path = path.Replace("jar:file://", "");
                    path = path.Replace("!/assets", "");
                    return path;
                }

                string appDataPath = Application.dataPath;
                string directoryPath = Path.GetDirectoryName(appDataPath);

                string[] possiblePaths =
                {
                    Path.Combine(directoryPath, "split_install_time_pack.apk"),
                    Path.Combine(directoryPath, "base.apk"),
                    appDataPath
                };

                foreach (string possiblePath in possiblePaths)
                {
                    if (File.Exists(possiblePath))
                    {
                        return possiblePath;
                    }
                }

                return appDataPath;
            }
            catch (Exception ex)
            {
                Debug.LogError($"获取APK路径时发生错误: {ex.Message}");
                return string.Empty;
            }
        }

        // 优化的I/O操作 - 使用缓冲区和批量写入
        public static IEnumerator DecompressionCoroutineOptimized(string zipFilePath, string destinationDirectory,
            bool overwrite = true, ZipCallback callback = null, int batchSize = 8)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }

            // 使用更大的缓冲区减少I/O次数
            const int BUFFER_SIZE = 64 * 1024; // 64KB缓冲区
            byte[] buffer = new byte[BUFFER_SIZE];

            // 预分配字符串构建器，避免重复分配
            var pathBuilder = new System.Text.StringBuilder(256);


            using (ZipFile zipFile = new ZipFile(zipFilePath))
            {
                // 一次性筛选所有条目，避免重复遍历
                var filteredEntries = PreFilterEntries(zipFile);

                if (filteredEntries.Count == 0)
                {
                    callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                    yield break;
                }

                // 预创建所有必要的目录，减少后续检查
                PreCreateDirectories(filteredEntries, destinationDirectory, pathBuilder);

                int totalEntries = filteredEntries.Count;
                int processedEntries = 0;

                // 批量处理文件，每批处理完后yield一次
                for (int i = 0; i < filteredEntries.Count; i += batchSize)
                {
                    int currentBatchSize = Math.Min(batchSize, filteredEntries.Count - i);

                    for (int j = 0; j < currentBatchSize; j++)
                    {
                        var entry = filteredEntries[i + j];

                        try
                        {
                            bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                            if (shouldProcess && !entry.IsDirectory)
                            {
                                // 使用预分配的StringBuilder构建路径
                                string targetPath = BuildTargetPath(entry.FileName, destinationDirectory, pathBuilder);

                                if (!string.IsNullOrEmpty(targetPath))
                                {
                                    // 优化的文件复制，使用大缓冲区
                                    ExtractFileOptimized(entry, targetPath, buffer, overwrite);

                                    callback?.OnPostUnzipCall?.Invoke(entry);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"解压文件 {entry.FileName} 时发生错误: {ex.Message}");
                            callback?.OnPackFinishedCall?.Invoke(false, $"解压过程中发生错误: {ex.Message}");
                            yield break;
                        }

                        processedEntries++;

                        // 只在批次结束时更新进度，减少回调频率
                        if (j == currentBatchSize - 1)
                        {
                            float progress = (float)processedEntries / totalEntries;
                            callback?.OnProgressCallback?.Invoke(progress);
                        }
                    }

                    yield return null; // 每批处理完后让出控制权
                }

                callback?.OnPackFinishedCall?.Invoke(true, "解压成功");
            }
        }

        // 优化的文件提取方法
        private static void ExtractFileOptimized(ZipEntry entry, string targetPath, byte[] buffer, bool overwrite)
        {
            // 如果文件存在且不覆盖，直接跳过
            if (!overwrite && File.Exists(targetPath))
            {
                return;
            }

            using (var entryStream = entry.OpenReader())
            {
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write, FileShare.None,
                           buffer.Length))
                {
                    int bytesRead;
                    while ((bytesRead = entryStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                    }

                    // 强制刷新到磁盘
                    fileStream.Flush();
                }
            }
        }

        // 内存优化的对象池
        public static class ZipUtilsObjectPool
        {
            private static readonly Queue<byte[]> _bufferPool = new Queue<byte[]>();

            private static readonly Queue<System.Text.StringBuilder> _stringBuilderPool =
                new Queue<System.Text.StringBuilder>();

            private static readonly object _lockObject = new object();

            public static byte[] GetBuffer(int size = 64 * 1024)
            {
                lock (_lockObject)
                {
                    if (_bufferPool.Count > 0)
                    {
                        var buffer = _bufferPool.Dequeue();
                        if (buffer.Length >= size)
                            return buffer;
                    }
                }

                return new byte[size];
            }

            public static void ReturnBuffer(byte[] buffer)
            {
                if (buffer != null && buffer.Length >= 32 * 1024) // 只回收较大的缓冲区
                {
                    lock (_lockObject)
                    {
                        if (_bufferPool.Count < 4) // 限制池大小
                        {
                            _bufferPool.Enqueue(buffer);
                        }
                    }
                }
            }

            public static System.Text.StringBuilder GetStringBuilder()
            {
                lock (_lockObject)
                {
                    if (_stringBuilderPool.Count > 0)
                    {
                        var sb = _stringBuilderPool.Dequeue();
                        sb.Clear();
                        return sb;
                    }
                }

                return new System.Text.StringBuilder(256);
            }

            public static void ReturnStringBuilder(System.Text.StringBuilder sb)
            {
                if (sb != null && sb.Capacity <= 1024) // 避免回收过大的StringBuilder
                {
                    lock (_lockObject)
                    {
                        if (_stringBuilderPool.Count < 4)
                        {
                            _stringBuilderPool.Enqueue(sb);
                        }
                    }
                }
            }
        }

        // 使用对象池的优化版本
        public static IEnumerator DecompressionCoroutineWithPool(string zipFilePath, string destinationDirectory,
            bool overwrite = true, ZipCallback callback = null, int batchSize = 8)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }

            byte[] buffer = ZipUtilsObjectPool.GetBuffer();
            var pathBuilder = ZipUtilsObjectPool.GetStringBuilder();

            try
            {
                using (ZipFile zipFile = new ZipFile(zipFilePath))
                {
                    var filteredEntries = PreFilterEntriesOptimized(zipFile);

                    if (filteredEntries.Count == 0)
                    {
                        callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                        yield break;
                    }

                    PreCreateDirectoriesOptimized(filteredEntries, destinationDirectory, pathBuilder);

                    int totalEntries = filteredEntries.Count;
                    int processedEntries = 0;

                    for (int i = 0; i < filteredEntries.Count; i += batchSize)
                    {
                        int currentBatchSize = Math.Min(batchSize, filteredEntries.Count - i);

                        for (int j = 0; j < currentBatchSize; j++)
                        {
                            var entry = filteredEntries[i + j];

                            try
                            {
                                bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                                if (shouldProcess && !entry.IsDirectory)
                                {
                                    string targetPath = BuildTargetPathOptimized(entry.FileName, destinationDirectory,
                                        pathBuilder);

                                    if (!string.IsNullOrEmpty(targetPath))
                                    {
                                        ExtractFileWithPool(entry, targetPath, buffer, overwrite);
                                        callback?.OnPostUnzipCall?.Invoke(entry);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogError($"解压文件 {entry.FileName} 时发生错误: {ex.Message}");
                                callback?.OnPackFinishedCall?.Invoke(false, $"解压过程中发生错误: {ex.Message}");
                                yield break;
                            }

                            processedEntries++;
                        }

                        // 批次完成后更新进度
                        float progress = (float)processedEntries / totalEntries;
                        callback?.OnProgressCallback?.Invoke(progress);

                        yield return null;
                    }

                    callback?.OnPackFinishedCall?.Invoke(true, "解压成功");
                }
            }
            finally
            {
                // 归还对象到池中
                ZipUtilsObjectPool.ReturnBuffer(buffer);
                ZipUtilsObjectPool.ReturnStringBuilder(pathBuilder);
            }
        }

        private static void ExtractFileWithPool(ZipEntry entry, string targetPath, byte[] buffer, bool overwrite)
        {
            if (!overwrite && File.Exists(targetPath))
                return;

            using (var entryStream = entry.OpenReader())
            {
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write,
                           FileShare.None, buffer.Length, FileOptions.SequentialScan))
                {
                    int bytesRead;
                    while ((bytesRead = entryStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                    }
                }
            }
        }

        // Unity协程优化版本
        public static IEnumerator DecompressionCoroutineUnityOptimized(string zipFilePath, string destinationDirectory,
            bool overwrite = true, ZipCallback callback = null)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }

            // 使用Unity的时间管理来控制每帧处理时间
            const float MAX_FRAME_TIME = 0.016f; // 16ms，保持60FPS
            var frameTimer = new System.Diagnostics.Stopwatch();

            byte[] buffer = ZipUtilsObjectPool.GetBuffer();
            var pathBuilder = ZipUtilsObjectPool.GetStringBuilder();

            try
            {
                using (ZipFile zipFile = new ZipFile(zipFilePath))
                {
                    var filteredEntries = PreFilterEntriesOptimized(zipFile);

                    if (filteredEntries.Count == 0)
                    {
                        callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                        yield break;
                    }

                    PreCreateDirectoriesOptimized(filteredEntries, destinationDirectory, pathBuilder);

                    int totalEntries = filteredEntries.Count;
                    int processedEntries = 0;

                    frameTimer.Start();

                    foreach (var entry in filteredEntries)
                    {
                        try
                        {
                            bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                            if (shouldProcess && !entry.IsDirectory)
                            {
                                string targetPath = BuildTargetPathOptimized(entry.FileName, destinationDirectory,
                                    pathBuilder);

                                if (!string.IsNullOrEmpty(targetPath))
                                {
                                    ExtractFileWithPool(entry, targetPath, buffer, overwrite);
                                    callback?.OnPostUnzipCall?.Invoke(entry);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"解压文件 {entry.FileName} 时发生错误: {ex.Message}");
                            callback?.OnPackFinishedCall?.Invoke(false, $"解压过程中发生错误: {ex.Message}");
                            yield break;
                        }

                        processedEntries++;

                        // 检查是否需要让出控制权
                        if (frameTimer.Elapsed.TotalSeconds >= MAX_FRAME_TIME)
                        {
                            float progress = (float)processedEntries / totalEntries;
                            callback?.OnProgressCallback?.Invoke(progress);

                            frameTimer.Restart();
                            yield return null; // 让出控制权给Unity主线程
                        }
                    }

                    // 最终进度更新
                    callback?.OnProgressCallback?.Invoke(1.0f);
                    callback?.OnPackFinishedCall?.Invoke(true, "解压成功");
                }
            }
            finally
            {
                ZipUtilsObjectPool.ReturnBuffer(buffer);
                ZipUtilsObjectPool.ReturnStringBuilder(pathBuilder);
                frameTimer.Stop();
            }
        }

        // 协程状态管理优化
        public class ZipExtractionState
        {
            public int CurrentIndex { get; set; }
            public int TotalEntries { get; set; }
            public long ProcessedBytes { get; set; }
            public long TotalBytes { get; set; }
            public System.Diagnostics.Stopwatch Timer { get; set; }
            public bool IsPaused { get; set; }

            public ZipExtractionState()
            {
                Timer = new System.Diagnostics.Stopwatch();
            }

            public float GetProgress()
            {
                return TotalEntries > 0 ? (float)CurrentIndex / TotalEntries : 0f;
            }

            public float GetByteProgress()
            {
                return TotalBytes > 0 ? (float)ProcessedBytes / TotalBytes : 0f;
            }
        }

        // 可暂停恢复的协程版本
        public static IEnumerator DecompressionCoroutineResumable(string zipFilePath, string destinationDirectory,
            ZipExtractionState state, bool overwrite = true, ZipCallback callback = null)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }

            const float MAX_FRAME_TIME = 0.016f;
            var frameTimer = new System.Diagnostics.Stopwatch();

            byte[] buffer = ZipUtilsObjectPool.GetBuffer();
            var pathBuilder = ZipUtilsObjectPool.GetStringBuilder();

            try
            {
                using (ZipFile zipFile = new ZipFile(zipFilePath))
                {
                    var filteredEntries = PreFilterEntriesOptimized(zipFile);

                    if (filteredEntries.Count == 0)
                    {
                        callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                        yield break;
                    }

                    // 初始化状态
                    if (state.CurrentIndex == 0)
                    {
                        state.TotalEntries = filteredEntries.Count;
                        state.TotalBytes = filteredEntries.Sum(e => e.UncompressedSize);
                        PreCreateDirectoriesOptimized(filteredEntries, destinationDirectory, pathBuilder);
                    }

                    state.Timer.Start();
                    frameTimer.Start();

                    // 从上次停止的位置继续
                    for (int i = state.CurrentIndex; i < filteredEntries.Count; i++)
                    {
                        // 检查是否暂停
                        while (state.IsPaused)
                        {
                            yield return null;
                        }

                        var entry = filteredEntries[i];

                        try
                        {
                            bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                            if (shouldProcess && !entry.IsDirectory)
                            {
                                string targetPath = BuildTargetPathOptimized(entry.FileName, destinationDirectory,
                                    pathBuilder);

                                if (!string.IsNullOrEmpty(targetPath))
                                {
                                    ExtractFileWithPool(entry, targetPath, buffer, overwrite);
                                    callback?.OnPostUnzipCall?.Invoke(entry);
                                }
                            }

                            state.ProcessedBytes += entry.UncompressedSize;
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"解压文件 {entry.FileName} 时发生错误: {ex.Message}");
                            callback?.OnPackFinishedCall?.Invoke(false, $"解压过程中发生错误: {ex.Message}");
                            yield break;
                        }

                        state.CurrentIndex = i + 1;

                        // 检查帧时间
                        if (frameTimer.Elapsed.TotalSeconds >= MAX_FRAME_TIME)
                        {
                            callback?.OnProgressCallback?.Invoke(state.GetProgress());
                            frameTimer.Restart();
                            yield return null;
                        }
                    }

                    callback?.OnProgressCallback?.Invoke(1.0f);
                    callback?.OnPackFinishedCall?.Invoke(true, "解压成功");
                }
            }
            finally
            {
                ZipUtilsObjectPool.ReturnBuffer(buffer);
                ZipUtilsObjectPool.ReturnStringBuilder(pathBuilder);
                state.Timer.Stop();
                frameTimer.Stop();
            }
        }

        // 智能批处理优化
        public static IEnumerator DecompressionCoroutineSmartBatch(string zipFilePath, string destinationDirectory,
            bool overwrite = true, ZipCallback callback = null)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }

            byte[] buffer = ZipUtilsObjectPool.GetBuffer();
            var pathBuilder = ZipUtilsObjectPool.GetStringBuilder();

            try
            {
                using (ZipFile zipFile = new ZipFile(zipFilePath))
                {
                    var filteredEntries = PreFilterEntriesOptimized(zipFile);

                    if (filteredEntries.Count == 0)
                    {
                        callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                        yield break;
                    }

                    PreCreateDirectoriesOptimized(filteredEntries, destinationDirectory, pathBuilder);

                    // 智能批处理：根据文件大小和数量动态调整批次大小
                    var batches = CreateSmartBatches(filteredEntries);

                    int totalEntries = filteredEntries.Count;
                    int processedEntries = 0;

                    foreach (var batch in batches)
                    {
                        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                        foreach (var entry in batch.Entries)
                        {
                            try
                            {
                                bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                                if (shouldProcess && !entry.IsDirectory)
                                {
                                    string targetPath = BuildTargetPathOptimized(entry.FileName, destinationDirectory,
                                        pathBuilder);

                                    if (!string.IsNullOrEmpty(targetPath))
                                    {
                                        ExtractFileWithPool(entry, targetPath, buffer, overwrite);
                                        callback?.OnPostUnzipCall?.Invoke(entry);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogError($"解压文件 {entry.FileName} 时发生错误: {ex.Message}");
                                callback?.OnPackFinishedCall?.Invoke(false, $"解压过程中发生错误: {ex.Message}");
                                yield break;
                            }

                            processedEntries++;
                        }

                        stopwatch.Stop();

                        // 根据处理时间动态调整下一批的大小
                        AdjustBatchSize(batch, stopwatch.ElapsedMilliseconds);

                        float progress = (float)processedEntries / totalEntries;
                        callback?.OnProgressCallback?.Invoke(progress);

                        yield return null;
                    }

                    callback?.OnPackFinishedCall?.Invoke(true, "解压成功");
                }
            }
            finally
            {
                ZipUtilsObjectPool.ReturnBuffer(buffer);
                ZipUtilsObjectPool.ReturnStringBuilder(pathBuilder);
            }
        }

        // 智能批次类
        private class SmartBatch
        {
            public List<ZipEntry> Entries { get; set; }
            public long TotalSize { get; set; }
            public int TargetProcessingTime { get; set; } // 目标处理时间（毫秒）
        }

        // 创建智能批次
        private static List<SmartBatch> CreateSmartBatches(List<ZipEntry> entries)
        {
            var batches = new List<SmartBatch>();
            const long TARGET_BATCH_SIZE = 2 * 1024 * 1024; // 2MB per batch
            const int MAX_BATCH_ENTRIES = 20;
            const int TARGET_PROCESSING_TIME = 16; // 16ms，保持60FPS

            var currentBatch = new SmartBatch
            {
                Entries = new List<ZipEntry>(),
                TotalSize = 0,
                TargetProcessingTime = TARGET_PROCESSING_TIME
            };

            foreach (var entry in entries)
            {
                // 如果添加当前文件会超过批次限制，创建新批次
                if ((currentBatch.TotalSize + entry.UncompressedSize > TARGET_BATCH_SIZE) ||
                    (currentBatch.Entries.Count >= MAX_BATCH_ENTRIES))
                {
                    if (currentBatch.Entries.Count > 0)
                    {
                        batches.Add(currentBatch);
                        currentBatch = new SmartBatch
                        {
                            Entries = new List<ZipEntry>(),
                            TotalSize = 0,
                            TargetProcessingTime = TARGET_PROCESSING_TIME
                        };
                    }
                }

                currentBatch.Entries.Add(entry);
                currentBatch.TotalSize += entry.UncompressedSize;
            }

            if (currentBatch.Entries.Count > 0)
            {
                batches.Add(currentBatch);
            }

            return batches;
        }

        // 动态调整批次大小
        private static void AdjustBatchSize(SmartBatch batch, long actualProcessingTime)
        {
            // 如果处理时间超过目标时间，减少下次批次大小
            if (actualProcessingTime > batch.TargetProcessingTime * 1.5)
            {
                // 这里可以实现动态调整逻辑
                // 例如：记录到全局变量中，影响下次批次创建
            }
        }

        // 基础的文件筛选算法
        private static List<ZipEntry> PreFilterEntries(ZipFile zipFile)
        {
            var filteredEntries = new List<ZipEntry>();

            foreach (ZipEntry entry in zipFile)
            {
                string fileName = entry.FileName.Replace('\\', '/');

                if (fileName.StartsWith("assets/"))
                {
                    if (fileName.IndexOf('/', 7) == -1 || fileName.StartsWith("assets/Video/"))
                    {
                        filteredEntries.Add(entry);
                    }
                }
            }

            return filteredEntries;
        }

        // 基础的目录预创建
        private static void PreCreateDirectories(List<ZipEntry> entries, string destinationDirectory,
            System.Text.StringBuilder pathBuilder)
        {
            var directoriesToCreate = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            // 预创建Video目录
            string videoPath = Path.Combine(destinationDirectory, "Video");
            directoriesToCreate.Add(videoPath);

            // 收集所有需要创建的目录
            foreach (var entry in entries)
            {
                if (entry.IsDirectory) continue;

                string targetPath = BuildTargetPath(entry.FileName, destinationDirectory, pathBuilder);
                string directory = Path.GetDirectoryName(targetPath);

                if (!string.IsNullOrEmpty(directory))
                {
                    directoriesToCreate.Add(directory);
                }
            }

            // 批量创建目录
            foreach (var directory in directoriesToCreate)
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
        }

        // 基础的路径构建
        private static string BuildTargetPath(string fileName, string destinationDirectory,
            System.Text.StringBuilder pathBuilder)
        {
            pathBuilder.Clear();
            pathBuilder.Append(destinationDirectory);

            fileName = fileName.Replace('\\', '/');

            // 处理路径：移除assets/前缀，直接解压到目标目录
            if (fileName.StartsWith("assets/Video/"))
            {
                // assets/Video/xxx.mp4 -> Video/xxx.mp4
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append("Video");
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append(fileName.Substring(13)); // 移除"assets/Video/"前缀
            }
            else if (fileName.StartsWith("assets/"))
            {
                // assets/xxx -> xxx (直接到根目录)
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append(fileName.Substring(7)); // 移除"assets/"前缀
            }
            else
            {
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append(fileName);
            }

            return pathBuilder.ToString();
        }

        // 优化的文件筛选算法
        public static List<ZipEntry> PreFilterEntriesOptimized(ZipFile zipFile)
        {
            var filteredEntries = new List<ZipEntry>();

            // 预编译正则表达式或使用更高效的字符串匹配
            const string ASSETS_PREFIX = "assets/";
            const string VIDEO_PREFIX = "assets/Video/";
            const int ASSETS_PREFIX_LENGTH = 7;
            const int VIDEO_PREFIX_LENGTH = 13;

            foreach (ZipEntry entry in zipFile)
            {
                if (entry.IsDirectory) continue;

                // 使用ReadOnlySpan减少字符串分配（.NET Core 2.1+）
                string fileName = entry.FileName;

                // 快速路径检查
                if (fileName.Length < ASSETS_PREFIX_LENGTH) continue;

                // 使用StartsWith的重载版本，避免创建新字符串
                if (fileName.StartsWith(ASSETS_PREFIX, StringComparison.Ordinal))
                {
                    // 优化的路径检查逻辑
                    if (fileName.Length == ASSETS_PREFIX_LENGTH || // assets/ 根目录文件
                        fileName.StartsWith(VIDEO_PREFIX, StringComparison.Ordinal) || // Video目录文件
                        fileName.IndexOf('/', ASSETS_PREFIX_LENGTH) == -1) // assets下的直接文件
                    {
                        filteredEntries.Add(entry);
                    }
                }
            }

            // 按文件大小排序，小文件优先处理，减少内存峰值
            filteredEntries.Sort((a, b) => a.UncompressedSize.CompareTo(b.UncompressedSize));

            return filteredEntries;
        }

        // 优化的路径构建
        public static string BuildTargetPathOptimized(string fileName, string destinationDirectory,
            System.Text.StringBuilder pathBuilder)
        {
            pathBuilder.Clear();
            pathBuilder.Append(destinationDirectory);

            // 快速路径处理，避免多次字符串操作
            if (fileName.StartsWith("assets/Video/", StringComparison.Ordinal))
            {
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append("Video");
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append(fileName, 13, fileName.Length - 13); // 跳过"assets/Video/"
            }
            else if (fileName.StartsWith("assets/", StringComparison.Ordinal))
            {
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append(fileName, 7, fileName.Length - 7); // 跳过"assets/"
            }
            else
            {
                pathBuilder.Append(Path.DirectorySeparatorChar);
                pathBuilder.Append(fileName);
            }

            return pathBuilder.ToString();
        }

        // 优化的目录预创建
        public static void PreCreateDirectoriesOptimized(List<ZipEntry> entries, string destinationDirectory,
            System.Text.StringBuilder pathBuilder)
        {
            var directoriesToCreate = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            // 收集所有需要创建的目录
            foreach (var entry in entries)
            {
                if (entry.IsDirectory) continue;

                string targetPath = BuildTargetPathOptimized(entry.FileName, destinationDirectory, pathBuilder);
                string directory = Path.GetDirectoryName(targetPath);

                if (!string.IsNullOrEmpty(directory))
                {
                    directoriesToCreate.Add(directory);
                }
            }

            // 批量创建目录
            foreach (var directory in directoriesToCreate)
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
        }
    }

    // 用于存储ZIP条目信息的类
    public class ZipEntryInfo
    {
        public string FileName { get; set; }
        public long UncompressedSize { get; set; }
        public bool IsDirectory { get; set; }
    }

    // 高性能解压工具类
    public static class HighPerformanceZipExtractor
    {
        // 修复版本：可靠的文件解压 - 确保文件复制成功
        public static IEnumerator DecompressionCoroutineMemoryMapped(string zipFilePath, string destinationDirectory,
            bool overwrite = true, ZipUtilsEx.ZipCallback callback = null, int maxParallelThreads = 8)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }

            const int LARGE_BUFFER_SIZE = 2 * 1024 * 1024; // 2MB缓冲区
            const long LARGE_FILE_THRESHOLD = 50 * 1024 * 1024; // 50MB阈值

            var tasks = new List<Task>();
            var semaphore = new SemaphoreSlim(maxParallelThreads, maxParallelThreads);
            var cancellationTokenSource = new CancellationTokenSource();

            try
            {
                using (var zipFile = new ZipFile(zipFilePath))
            {
                var filteredEntries = ZipUtilsEx.PreFilterEntriesOptimized(zipFile);

                if (filteredEntries.Count == 0)
                {
                    callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                    yield break;
                }

                // 按文件大小排序，大文件优先处理
                filteredEntries.Sort((a, b) => b.UncompressedSize.CompareTo(a.UncompressedSize));

                ZipUtilsEx.PreCreateDirectoriesOptimized(filteredEntries, destinationDirectory,
                    new System.Text.StringBuilder());

                int totalEntries = filteredEntries.Count;
                int processedEntries = 0;
                var lockObject = new object();

                // 分批处理文件
                const int BATCH_SIZE = 5;
                for (int i = 0; i < filteredEntries.Count; i += BATCH_SIZE)
                {
                    var batch = filteredEntries.Skip(i).Take(BATCH_SIZE).ToList();

                    var task = Task.Run(() =>
                    {
                        semaphore.Wait(); // 移到任务内部
                        try
                        {
                            var buffer = new byte[LARGE_BUFFER_SIZE];
                            var pathBuilder = new System.Text.StringBuilder();

                            foreach (var entry in batch)
                            {
                                bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                                if (shouldProcess && !entry.IsDirectory)
                                {
                                    string targetPath = ZipUtilsEx.BuildTargetPathOptimized(entry.FileName,
                                        destinationDirectory, pathBuilder);

                                    if (!string.IsNullOrEmpty(targetPath))
                                    {
                                        if (entry.UncompressedSize > LARGE_FILE_THRESHOLD)
                                        {
                                            ExtractLargeFileOptimized(entry, targetPath, buffer, overwrite);
                                        }
                                        else
                                        {
                                            ExtractSmallFileOptimized(entry, targetPath, buffer, overwrite);
                                        }

                                        callback?.OnPostUnzipCall?.Invoke(entry);
                                    }
                                }

                                lock (lockObject)
                                {
                                    processedEntries++;
                                }
                            }

                            lock (lockObject)
                            {
                                float progress = (float)processedEntries / totalEntries;
                                callback?.OnProgressCallback?.Invoke(progress);
                            }
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }, cancellationTokenSource.Token);

                    tasks.Add(task);
                }

                // 等待所有任务完成
                while (tasks.Any(t => !t.IsCompleted))
                {
                    yield return null;
                    tasks.RemoveAll(t => t.IsCompleted);
                }

                // 验证所有文件的完整性
                bool allFilesValid =
                    FileIntegrityValidator.ValidateAllExtractedFiles(filteredEntries, destinationDirectory);

                callback?.OnProgressCallback?.Invoke(1.0f);

                if (allFilesValid)
                {
                    callback?.OnPackFinishedCall?.Invoke(true, "解压成功，所有文件完整性验证通过");
                }
                else
                {
                    callback?.OnPackFinishedCall?.Invoke(false, "解压完成，但部分文件完整性验证失败");
                }
            } // 关闭using块
            }
            catch (Exception ex)
            {
                cancellationTokenSource.Cancel();
                Debug.LogError($"DecompressionCoroutineMemoryMapped 发生异常: {ex.Message}");
                callback?.OnPackFinishedCall?.Invoke(false, $"解压失败: {ex.Message}");
            }
            finally
            {
                semaphore?.Dispose();
                cancellationTokenSource?.Dispose();
            }
        }

        // 优化的大文件提取 - 增强版本，确保完整性
        private static void ExtractLargeFileOptimized(ZipEntry entry, string targetPath, byte[] buffer, bool overwrite)
        {
            if (!overwrite && File.Exists(targetPath))
                return;

            try
            {
                // 确保目标目录存在
                string directory = Path.GetDirectoryName(targetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                long totalBytesRead = 0;
                long expectedSize = entry.UncompressedSize;

                using (var entryStream = entry.OpenReader())
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write,
                           FileShare.None, Math.Min(buffer.Length, 1024 * 1024), FileOptions.SequentialScan))
                {
                    int bytesRead;
                    while ((bytesRead = entryStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;
                    }

                    // 强制刷新到磁盘
                    fileStream.Flush();
                }

                // 验证文件大小
                var fileInfo = new FileInfo(targetPath);
                if (fileInfo.Length != expectedSize)
                {
                    Debug.LogError($"文件大小不匹配: {entry.FileName}, 期望: {expectedSize}, 实际: {fileInfo.Length}");
                    // 删除不完整的文件
                    if (File.Exists(targetPath))
                    {
                        File.Delete(targetPath);
                    }

                    throw new InvalidDataException($"文件解压不完整: {entry.FileName}");
                }

                Debug.Log($"大文件解压成功: {entry.FileName}, 大小: {totalBytesRead} bytes");
            }
            catch (Exception ex)
            {
                Debug.LogError($"大文件解压失败: {entry.FileName}, 错误: {ex.Message}");
                // 清理不完整的文件
                if (File.Exists(targetPath))
                {
                    try
                    {
                        File.Delete(targetPath);
                    }
                    catch
                    {
                    }
                }

                throw;
            }
        }

        // 优化的小文件同步提取 - 增强版本，确保完整性
        private static void ExtractSmallFileOptimized(ZipEntry entry, string targetPath, byte[] buffer, bool overwrite)
        {
            if (!overwrite && File.Exists(targetPath))
                return;

            try
            {
                // 确保目标目录存在
                string directory = Path.GetDirectoryName(targetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                long totalBytesRead = 0;
                long expectedSize = entry.UncompressedSize;

                using (var entryStream = entry.OpenReader())
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write,
                           FileShare.None, 64 * 1024, FileOptions.SequentialScan))
                {
                    int bytesRead;
                    while ((bytesRead = entryStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;
                    }

                    // 强制刷新到磁盘
                    fileStream.Flush();
                }

                // 验证文件大小
                var fileInfo = new FileInfo(targetPath);
                if (fileInfo.Length != expectedSize)
                {
                    Debug.LogError($"小文件大小不匹配: {entry.FileName}, 期望: {expectedSize}, 实际: {fileInfo.Length}");
                    // 删除不完整的文件
                    if (File.Exists(targetPath))
                    {
                        File.Delete(targetPath);
                    }

                    throw new InvalidDataException($"小文件解压不完整: {entry.FileName}");
                }

                Debug.Log($"小文件解压成功: {entry.FileName}, 大小: {totalBytesRead} bytes");
            }
            catch (Exception ex)
            {
                Debug.LogError($"小文件解压失败: {entry.FileName}, 错误: {ex.Message}");
                // 清理不完整的文件
                if (File.Exists(targetPath))
                {
                    try
                    {
                        File.Delete(targetPath);
                    }
                    catch
                    {
                    }
                }

                throw;
            }
        }

        // 超高性能APK资源解压 - 专门针对Android优化
        public static IEnumerator DecompressApkAssetsUltraFast(string destinationDirectory,
            bool overwrite = true, ZipUtilsEx.ZipCallback callback = null)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                callback?.OnPackFinishedCall?.Invoke(false, "错误: 此方法仅适用于Android平台");
                yield break;
            }

            string apkPath = ZipUtilsEx.GetApkPath();
            if (string.IsNullOrEmpty(apkPath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, "错误: 无法获取APK路径");
                yield break;
            }

            Debug.Log($"开始超高性能解压APK中的assets目录: {apkPath}");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 使用最大线程数
            int maxThreads = Mathf.Max(4, System.Environment.ProcessorCount);

            yield return DecompressionCoroutineMemoryMapped(
                apkPath,
                destinationDirectory,
                overwrite,
                callback,
                maxThreads);

            stopwatch.Stop();
            Debug.Log($"超高性能assets目录解压完成，耗时: {stopwatch.Elapsed.TotalSeconds:F2}秒");
        }

        // 简化版本：确保文件复制成功的可靠实现
        public static IEnumerator DecompressionCoroutineReliable(string zipFilePath, string destinationDirectory,
            bool overwrite = true, ZipUtilsEx.ZipCallback callback = null, int maxParallelThreads = 4)
        {
            if (!File.Exists(zipFilePath))
            {
                callback?.OnPackFinishedCall?.Invoke(false, $"错误: 文件不存在 - {zipFilePath}");
                yield break;
            }

            Debug.Log($"开始可靠解压: {zipFilePath} -> {destinationDirectory}");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                using (var zipFile = new ZipFile(zipFilePath))
                {
                    var filteredEntries = ZipUtilsEx.PreFilterEntriesOptimized(zipFile);

                    if (filteredEntries.Count == 0)
                    {
                        callback?.OnPackFinishedCall?.Invoke(true, "没有找到符合条件的文件");
                        yield break;
                    }

                    Debug.Log($"找到 {filteredEntries.Count} 个文件需要解压");

                    // 预创建目录
                    ZipUtilsEx.PreCreateDirectoriesOptimized(filteredEntries, destinationDirectory, new System.Text.StringBuilder());

                    int totalEntries = filteredEntries.Count;
                    int processedEntries = 0;
                    int successCount = 0;
                    int failCount = 0;

                    // 使用简单的顺序处理，确保可靠性
                    foreach (var entry in filteredEntries)
                    {
                        try
                        {
                            bool shouldProcess = callback?.OnPreUnzipCall?.Invoke(entry) ?? true;

                            if (shouldProcess && !entry.IsDirectory)
                            {
                                var pathBuilder = new System.Text.StringBuilder();
                                string targetPath = ZipUtilsEx.BuildTargetPathOptimized(entry.FileName, destinationDirectory, pathBuilder);

                                if (!string.IsNullOrEmpty(targetPath))
                                {
                                    bool extractSuccess = ExtractFileReliable(entry, targetPath, overwrite);

                                    if (extractSuccess)
                                    {
                                        successCount++;
                                        callback?.OnPostUnzipCall?.Invoke(entry);
                                        Debug.Log($"成功解压: {entry.FileName} ({entry.UncompressedSize} bytes)");
                                    }
                                    else
                                    {
                                        failCount++;
                                        Debug.LogError($"解压失败: {entry.FileName}");
                                    }
                                }
                            }

                            processedEntries++;
                            float progress = (float)processedEntries / totalEntries;
                            callback?.OnProgressCallback?.Invoke(progress);

                            // 每处理10个文件让出一帧
                            if (processedEntries % 10 == 0)
                            {
                                yield return null;
                            }
                        }
                        catch (Exception ex)
                        {
                            failCount++;
                            Debug.LogError($"处理文件时发生异常: {entry.FileName}, 错误: {ex.Message}");
                        }
                    }

                    stopwatch.Stop();
                    Debug.Log($"解压完成，耗时: {stopwatch.Elapsed.TotalSeconds:F2}秒");
                    Debug.Log($"成功: {successCount}, 失败: {failCount}, 总计: {totalEntries}");

                    callback?.OnProgressCallback?.Invoke(1.0f);

                    if (failCount == 0)
                    {
                        callback?.OnPackFinishedCall?.Invoke(true, $"解压成功，处理了 {successCount} 个文件");
                    }
                    else
                    {
                        callback?.OnPackFinishedCall?.Invoke(false, $"解压完成但有 {failCount} 个文件失败");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"解压过程发生异常: {ex.Message}");
                callback?.OnPackFinishedCall?.Invoke(false, $"解压失败: {ex.Message}");
            }
        }

        // 可靠的文件提取方法
        private static bool ExtractFileReliable(ZipEntry entry, string targetPath, bool overwrite)
        {
            if (!overwrite && File.Exists(targetPath))
            {
                Debug.Log($"文件已存在，跳过: {targetPath}");
                return true;
            }

            try
            {
                // 确保目标目录存在
                string directory = Path.GetDirectoryName(targetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                long expectedSize = entry.UncompressedSize;
                long totalBytesRead = 0;
                const int BUFFER_SIZE = 64 * 1024; // 64KB缓冲区
                byte[] buffer = new byte[BUFFER_SIZE];

                using (var entryStream = entry.OpenReader())
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    int bytesRead;
                    while ((bytesRead = entryStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;
                    }

                    fileStream.Flush();
                }

                // 验证文件大小
                var fileInfo = new FileInfo(targetPath);
                if (fileInfo.Length != expectedSize)
                {
                    Debug.LogError($"文件大小不匹配: {entry.FileName}");
                    Debug.LogError($"期望: {expectedSize} bytes, 实际: {fileInfo.Length} bytes");

                    // 删除不完整的文件
                    if (File.Exists(targetPath))
                    {
                        File.Delete(targetPath);
                    }
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"提取文件失败: {entry.FileName}, 错误: {ex.Message}");

                // 清理不完整的文件
                if (File.Exists(targetPath))
                {
                    try { File.Delete(targetPath); } catch { }
                }
                return false;
            }
        }
    }

    // 文件完整性验证工具
    public static class FileIntegrityValidator
    {
        // 验证解压文件的完整性
        public static bool ValidateExtractedFile(ZipEntry entry, string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Debug.LogError($"文件不存在: {filePath}");
                    return false;
                }

                var fileInfo = new FileInfo(filePath);
                long expectedSize = entry.UncompressedSize;
                long actualSize = fileInfo.Length;

                if (actualSize != expectedSize)
                {
                    Debug.LogError($"文件大小不匹配: {entry.FileName}");
                    Debug.LogError($"期望大小: {expectedSize} bytes, 实际大小: {actualSize} bytes");
                    return false;
                }

                // 可以添加CRC校验等更严格的验证
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"验证文件完整性时发生错误: {ex.Message}");
                return false;
            }
        }

        // 批量验证文件完整性
        public static bool ValidateAllExtractedFiles(List<ZipEntry> entries, string destinationDirectory)
        {
            int validCount = 0;
            int totalCount = 0;
            var pathBuilder = new System.Text.StringBuilder();

            foreach (var entry in entries)
            {
                if (entry.IsDirectory) continue;

                totalCount++;
                string targetPath =
                    ZipUtilsEx.BuildTargetPathOptimized(entry.FileName, destinationDirectory, pathBuilder);

                if (ValidateExtractedFile(entry, targetPath))
                {
                    validCount++;
                }
            }

            Debug.Log($"文件完整性验证结果: {validCount}/{totalCount} 文件通过验证");
            return validCount == totalCount;
        }
    }

    // 文件系统优化工具
    public static class FileSystemOptimizer
    {
        // 预分配磁盘空间以提高写入性能
        public static void PreallocateSpace(string filePath, long size)
        {
            try
            {
                using (var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    fs.SetLength(size);
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"预分配空间失败: {ex.Message}");
            }
        }

        // 批量预创建文件以减少文件系统开销
        public static void BatchPreCreateFiles(List<ZipEntry> entries, string destinationDirectory)
        {
            var pathBuilder = new System.Text.StringBuilder();

            foreach (var entry in entries)
            {
                if (entry.IsDirectory) continue;

                string targetPath =
                    ZipUtilsEx.BuildTargetPathOptimized(entry.FileName, destinationDirectory, pathBuilder);

                if (!string.IsNullOrEmpty(targetPath) && entry.UncompressedSize > 0)
                {
                    try
                    {
                        // 预分配文件空间
                        PreallocateSpace(targetPath, entry.UncompressedSize);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"预创建文件失败 {targetPath}: {ex.Message}");
                    }
                }
            }
        }

        // 设置文件系统缓存策略
        public static void OptimizeFileSystemCache()
        {
            // 在Android上可以通过JNI调用优化文件系统缓存
            // 这里提供一个框架，具体实现需要根据平台调整
            try
            {
                // 强制刷新文件系统缓存
                System.GC.Collect();
                System.GC.WaitForPendingFinalizers();
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"优化文件系统缓存失败: {ex.Message}");
            }
        }
    }
}