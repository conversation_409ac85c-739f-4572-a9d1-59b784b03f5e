﻿using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using Ionic.Zip;

namespace HotFix
{
    public class ApkUtilsEx
    {
        // 解压回调定义
        public class ZipCallback
        {
            public Action<bool> OnPackFinishedCall; // 解压完成回调
            public Action<ZipEntry> OnPostUnzipCall; // 文件解压后回调
            public Action<float> OnProgressCallback; // 进度更新回调
            public Func<ZipEntry, bool> OnPreUnzipCall; // 文件预过滤回调
        }

        // 从APK中多线程解压文件的主方法
        public static IEnumerator DecompressFromApkAsync(
            string apkPath,
            string outputDirectory,
            bool overwriteExisting = true,
            ZipCallback callback = null,
            int maxConcurrency = 4)
        {
            // 输入验证
            if (!File.Exists(apkPath))
            {
                Debug.LogError($"APK文件不存在: {apkPath}");
                callback?.OnPackFinishedCall?.Invoke(false);
                yield break;
            }

            // 创建输出目录
            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            // 确保主线程调度器存在
            UnityMainThreadDispatcher.EnsureExists();

            var filesToExtract = new ConcurrentQueue<ZipEntry>();
            int totalFiles = 0;
            int processedFiles = 0;
            object progressLock = new object();

            //try
            //{
            // 打开APK文件（ZIP格式）
            using (ZipFile zipFile = ZipFile.Read(apkPath))
            {
                // 创建默认的预过滤回调（如果用户未提供）
                var originalPreFilter = callback?.OnPreUnzipCall;
                callback = callback ?? new ZipCallback();

                // 设置预过滤逻辑：只处理assets/和assets/Video/下的文件
                callback.OnPreUnzipCall = entry =>
                {
                    // 应用用户自定义过滤（如果有）
                    if (originalPreFilter != null && !originalPreFilter(entry))
                        return false;

                    // 跳过目录
                    if (entry.IsDirectory)
                        return false;

                    // 检查路径是否以assets/开头
                    if (!entry.FileName.StartsWith("assets/"))
                        return false;

                    // 允许assets/Video/下的所有文件
                    if (entry.FileName.StartsWith("assets/Video/"))
                        return true;

                    // 允许assets/下的直接文件（但不包括子目录）
                    return !entry.FileName.Substring("assets/".Length).Contains("/");
                };

                // 筛选需要解压的文件
                foreach (ZipEntry entry in zipFile)
                {
                    bool shouldExtract = callback.OnPreUnzipCall(entry);
                    if (shouldExtract)
                    {
                        filesToExtract.Enqueue(entry);
                        totalFiles++;
                    }
                }

                // 创建并启动工作任务
                var tasks = new List<Task>();
                maxConcurrency = Math.Max(1, Math.Min(maxConcurrency, Environment.ProcessorCount));

                for (int i = 0; i < maxConcurrency; i++)
                {
                    tasks.Add(Task.Run(() => ProcessFiles(
                        filesToExtract,
                        outputDirectory,
                        overwriteExisting,
                        callback,
                        ref processedFiles,
                        totalFiles,
                        progressLock
                    )));
                }

                // 等待所有任务完成
                while (!tasks.All(t => t.IsCompleted))
                {
                    yield return null; // 每帧让出控制权
                }

                // 检查是否有任务出错
                bool anyFailed = tasks.Any(t => t.IsFaulted || t.IsCanceled);
                callback?.OnPackFinishedCall?.Invoke(!anyFailed);
            }
            /*}
            catch (Exception ex)
            {
                Debug.LogError($"解压过程中发生致命错误: {ex}");
                callback?.OnPackFinishedCall?.Invoke(false);
            }*/
        }

        // 工作线程处理文件解压
        private static void ProcessFiles(
            ConcurrentQueue<ZipEntry> filesToExtract,
            string outputDirectory,
            bool overwriteExisting,
            ZipCallback callback,
            ref int processedFiles,
            int totalFiles,
            object progressLock)
        {
            while (filesToExtract.TryDequeue(out ZipEntry entry))
            {
                try
                {
                    // 获取文件名（不含路径）
                    string fileName = Path.GetFileName(entry.FileName);
                    if (string.IsNullOrEmpty(fileName))
                        continue;

                    // 构建输出路径
                    string outputPath = Path.Combine(outputDirectory, fileName);

                    // 检查是否需要跳过已存在的文件
                    if (!overwriteExisting && File.Exists(outputPath))
                    {
                        UpdateProgress(callback, ref processedFiles, totalFiles, progressLock);
                        continue;
                    }

                    // 调用解压前回调（在主线程执行）
                    UnityMainThreadDispatcher.Enqueue(() => { callback?.OnPostUnzipCall?.Invoke(entry); });

                    // 使用流操作解压文件
                    using (FileStream outputStream = File.Create(outputPath))
                    using (Stream inputStream = entry.OpenReader())
                    {
                        byte[] buffer = new byte[8192]; // 增大缓冲区提高吞吐量
                        int bytesRead;

                        while ((bytesRead = inputStream.Read(buffer, 0, buffer.Length)) > 0)
                        {
                            outputStream.Write(buffer, 0, bytesRead);
                        }
                    }

                    // 设置文件时间戳
                    File.SetLastWriteTime(outputPath, entry.LastModified);

                    // 更新进度
                    UpdateProgress(callback, ref processedFiles, totalFiles, progressLock);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"解压文件 {entry.FileName} 时出错: {ex.Message}");

                    // 清理可能存在的部分文件
                    try
                    {
                        string filePath = Path.Combine(outputDirectory, Path.GetFileName(entry.FileName));
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                        }
                    }
                    catch
                    {
                        /* 忽略清理错误 */
                    }
                }
            }
        }

        // 更新进度（线程安全）
        private static void UpdateProgress(
            ZipCallback callback,
            ref int processedFiles,
            int totalFiles,
            object progressLock)
        {
            int currentCount;
            float progress;

            lock (progressLock)
            {
                currentCount = Interlocked.Increment(ref processedFiles);
                progress = (float)currentCount / totalFiles;
            }

            // 在主线程更新UI
            HotfixManager.Enqueue(() => { callback?.OnProgressCallback?.Invoke(progress); });
        }
    }

    // 主线程调度器（确保在主线程执行代码）
    public class UnityMainThreadDispatcher : MonoBehaviour
    {
        private static UnityMainThreadDispatcher instance;
        private static readonly Queue<Action> executionQueue = new Queue<Action>();

        void Awake()
        {
            if (instance != null)
            {
                Destroy(gameObject);
                return;
            }

            instance = this;
            DontDestroyOnLoad(gameObject);
        }

        void Update()
        {
            lock (executionQueue)
            {
                while (executionQueue.Count > 0)
                {
                    executionQueue.Dequeue().Invoke();
                }
            }
        }

        public static void Enqueue(Action action)
        {
            if (action == null)
            {
                throw new ArgumentNullException(nameof(action));
            }

            lock (executionQueue)
            {
                executionQueue.Enqueue(action);
            }
        }

        public static void EnsureExists()
        {
            if (instance == null)
            {
                GameObject dispatcher = new GameObject("UnityMainThreadDispatcher");
                dispatcher.AddComponent<UnityMainThreadDispatcher>();
            }
        }
    }
}